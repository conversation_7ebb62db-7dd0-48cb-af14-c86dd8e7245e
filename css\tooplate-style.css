/*

Tooplate 2115 Marvel

https://www.tooplate.com/view/2115-marvel

*/

@import url("https://fonts.googleapis.com/css?family=Maven+Pro:400,700");
body {
    font-family: "Maven Pro", sans-serif;
    padding-top: 70px;
}

.full-screen {
    padding: 8rem 0;
}

.git {
    margin-left: 10px;
}

.small-text {
    color: #5b5b5b;
    font-size: 14px;
    font-weight: bold;
    padding: 8px 16px;
    border-radius: 50px;
    letter-spacing: 0.2px;
}

ul {
    margin: 0;
    padding: 0;
}

ul li {
    list-style: none;
}

a {
    font-weight: normal;
    text-decoration: none !important;
    transition: all 0.4s ease;
}

a:hover {
    color: #ffc200 !important;
}

.navbar-brand .uil {
    font-size: 40px;
}

p {
    font-size: 18px;
    font-weight: 300;
    line-height: 1.5;
    color: #5b5b5b;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: bold;
    letter-spacing: -1px;
}

h1 {
    color: #212121;
    font-size: 2.8em;
    margin: 24px 0;
}

h2 {
    color: #353535;
    font-size: 2.4em;
    font-weight: bold;
}

h3 {
    color: #484848;
}

h3,
b,
strong {
    font-weight: bold;
}

.skill {
    font-weight: bold;
    font-size: 20px;
    margin-bottom: auto;
    color: #5b5b5b;
}

.custom-btn {
    background: #eee;
    color: #5b5b5b;
    font-weight: bold;
    border-radius: 50px;
    padding: 13px 29px;
    font-size: 14px;
    line-height: normal;
    overflow: hidden;
    transition: all 0.4s ease;
}

.custom-btn:hover {
    color: #ffc200;
}

.custom-btn.custom-btn-bg {
    background: #474559;
    color: #ffffff;
}

.custom-btn.custom-btn-bg:hover {
    background: #ffc200;
    color: #ffffff !important;
}

.animated {
    position: relative;
}

.animated-info {
    display: inline-block;
    vertical-align: top;
    margin-top: 5px;
    min-width: 260px;
    position: relative;
}

.animated-item {
    color: #ffc200;
}

.animated-item {
    font-size: 38px;
    line-height: inherit;
    display: block;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    animation: BottomTotop 4s linear infinite 0s;
}

.animated-item:nth-child(2n+2) {
    animation-delay: 2s;
}

.animated-item:nth-child(3n+3) {
    animation-delay: 4s;
}

@keyframes BottomTotop {
    0% {
        opacity: 0;
    }
    5% {
        opacity: 0;
        transform: translateY(5px);
    }
    10% {
        opacity: 1;
        transform: translateY(0px);
    }
    25% {
        opacity: 1;
        transform: translateY(0px);
    }
    30% {
        opacity: 0;
        transform: translateY(5px);
    }
    80% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

.navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 999999;
    will-change: transform;
    transition: transform 200ms linear;
}

.navbar[class*="-unpinned"] {
    transform: translate(0, -150%);
}

.navbar[class*="-pinned"] {
    transform: translate(0, 0);
}

.navbar[class*="headroom--not-top"] {
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    padding-top: 0;
    padding-bottom: 0;
}

.navbar-brand {
    font-weight: bold;
}

.navbar-expand-sm .navbar-nav .nav-link {
    padding: 0 20px;
}

.nav-link {
    font-weight: bold;
    font-size: 16px;
    overflow: hidden;
}

.nav-link span {
    position: relative;
    display: inline-block;
    transition: transform 0.3s;
}

.nav-link span:before {
    position: absolute;
    top: 100%;
    content: attr(data-hover);
    transform: translate3d(0, 0, 0);
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: #ffc200;
    font-weight: bold;
}

.navbar-light .navbar-nav .nav-link:focus span,
.navbar-light .navbar-nav .nav-link:hover span {
    transform: translateY(-100%);
}

.navbar-light .navbar-toggler-icon {
    background: none;
}

.navbar-toggler {
    border: 0;
    padding: 0;
    width: 32px;
    height: 32px;
    line-height: 32px;
    outline: none;
    cursor: pointer;
    margin-right: 10px;
}

.navbar-toggler:focus {
    outline: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:first-child {
    transform: rotate(45deg);
    top: 6px;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:nth-child(2) {
    display: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:last-child {
    transform: rotate(-45deg);
    bottom: 1px;
}

.navbar-toggler .navbar-toggler-icon {
    background: #212121;
    display: block;
    width: 100%;
    height: 2px;
    margin: 5px 0;
    transition: all 0.4s ease;
    position: relative;
}

.copyright-text {
    font-size: 16px;
    font-weight: normal;
    display: block;
}

.color-mode {
    font-weight: bold;
    cursor: pointer;
}

.color-mode-icon {
    position: relative;
    right: 6px;
}

.color-mode-icon:after {
    font-family: 'unicons';
    content: '\ea9f';
    font-size: 30px;
    font-weight: 300;
    font-style: normal;
}

.color-mode-icon.active:after {
    font-family: 'unicons';
    content: '\eb65';
    font-size: 30px;
    color: #ffffff;
}

.dark-mode {
    background: #0c0c0d;
}

.dark-mode .navbar-light .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.dark-mode .navbar-light .navbar-nav .nav-link:hover {
    color: #ffc200;
}

.dark-mode .navbar[class*="headroom--not-top"] {
    background: #0c0c0d;
    border-bottom: 1px solid #1f1f1f;
}

.dark-mode .small-text {
    background: #0d0c15;
    color: #ffffff;
}

.dark-mode .feature-card .uil,
.dark-mode .navbar-light .navbar-brand,
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode .color-mode {
    color: #ffffff;
}

.dark-mode .owl-carousel .owl-nav button.owl-next,
.dark-mode .owl-carousel .owl-nav button.owl-prev,
.dark-mode .owl-carousel button.owl-dot {
    color: #ffffff;
}

.timeline-wrapper {
    position: relative;
    padding: 22px 0;
}

.timeline-wrapper:last-child:before {
    height: 0;
}

.timeline-wrapper:before {
    content: "";
    background: #474559;
    width: 3px;
    height: 100%;
    position: absolute;
    left: 38px;
}

.timeline-yr {
    background: #474559;
    border-radius: 100%;
    position: absolute;
    width: 75px;
    height: 75px;
    line-height: 75px;
    text-align: center;
}

.timeline-yr span {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    display: block;
    line-height: 75px;
}

.timeline-info {
    display: inline-block;
    vertical-align: top;
    max-width: 432px;
    margin-top: 5em;
    margin-left: 6em;
}

.timeline-info small {
    color: #474559;
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
}

.owl-carousel .owl-nav span {
    display: none;
}

.owl-carousel .owl-nav .owl-prev,
.owl-carousel .owl-nav .owl-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: white;  /* Background color for arrows */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Left Arrow */
.owl-carousel .owl-nav .owl-prev {
    left: -60px; /* Adjust position */
}

.owl-carousel .owl-nav .owl-next {
    right: -60px; /* Adjust position */
}

/* Arrow Icons */
.owl-carousel .owl-nav .owl-prev:before,
.owl-carousel .owl-nav .owl-next:before {
    content: "‹"; /* Unicode for left arrow */
    font-size: 30px;
    color: black; /* Arrow color */
    font-family: Arial, sans-serif; /* Change to a standard font */
}

/* Right Arrow */
.owl-carousel .owl-nav .owl-next:before {
    content: "›"; /* Unicode for right arrow */
}


.owl-theme .owl-nav [class*=owl-] {
    background: none;
    border-radius: 0;
    margin: 0;
    padding: 0;
    font-size: inherit;
}

.owl-theme .owl-nav [class*=owl-]:hover {
    color: #ffc200;
}

.owl-theme .owl-dots .owl-dot {
    outline: none;
}

.owl-theme .owl-dots .owl-dot span {
    width: 8px;
    height: 8px;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
    background: #5b5b5b;
}

.google-map iframe {
    width: 100%;
}

#contact {
    width: 80%;
    margin: auto;
}

.contact-form {
    position: relative;
}

.contact-form .form-control {
    background: transparent;
    border-radius: 2px;
    outline: none;
    box-shadow: none;
    font-weight: bold;
    margin: 16px 0;
}

.contact-form .form-control:not(textarea) {
    height: 48px;
}

.contact-form .form-control:hover,
.contact-form .form-control:focus {
    border-color: #ffc200;
}

.contact-form .submit-btn {
    background: #ffc200;
    border-radius: 50px;
    color: #ffffff;
    font-weight: bold;
    border: 0;
    cursor: pointer;
    transition: all 0.4s ease;
}

.contact-form .submit-btn:hover {
    background: #474559;
}

.contact-info {
    background: #474559;
    border-radius: 0 0 3px 3px;
    position: relative;
    bottom: 8px;
}

.contact-info p,
.contact-info a {
    color: #f7f3f3;
}

.social-links .uil {
    color: #f7f3f3;
    font-size: 20px;
    display: block;
    margin: 5px 0;
}

.social-links .uil:hover {
    color: #ffc200;
}

.icon {
    cursor: pointer;
    height: 2rem;
}

.contact-icon {
    cursor: default;
}

.email-icon {
    height: 2.5rem;
}

.section__text__p1 {
    text-align: center;
}

.title {
    font-size: 3rem;
    text-align: center;
}

.contact-info-upper-container {
    display: flex;
    justify-content: space-evenly;
    border-radius: 2rem;
    border: rgb(53, 53, 53) 0.1rem solid;
    border-color: rgb(163, 163, 163);
    background: (250, 250, 250);
    margin: 2rem 20%;
    padding: 0.5rem;
}

.about-containers {
    gap: 2rem;
    margin: auto;
    margin-bottom: 2rem;
    margin-top: 2rem;
    max-width: 80%;
}

article {
    display: flex;
    width: 10rem;
    justify-content: space-around;
    gap: 0.5rem;
}

article .icon {
    cursor: default;
}

.article-container {
    display: flex;
    text-align: initial;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 2.5rem;
    justify-content: space-around;
}

.details-container {
    padding: 1.5rem;
    flex: 1;
    background: white;
    border-radius: 2rem;
    border: rgb(53, 53, 53) 0.1rem solid;
    border-color: rgb(163, 163, 163);
    text-align: center;
}

.about-details-container {
    justify-content: center;
    flex-direction: column;
}

.experience-sub-title {
    color: rgb(85, 85, 85);
    font-weight: 600;
    font-size: 1.75rem;
    margin-bottom: 2rem;
}

#experience {
    position: relative;
    margin-top: 5%;
}

.about-containers,
.about-details-container {
    display: flex;
}

.experience-details-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.contact-info-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 1rem;
}

a {
    color: black;
    text-decoration: none;
    text-decoration-color: white;
    transition: all 300ms ease;
}

a:hover {
    color: grey;
    text-decoration: underline;
    text-underline-offset: 1rem;
    text-decoration-color: rgb(181, 181, 181);
}

@media (min-width: 1270px) {
    .owl-theme .owl-dots {
        position: relative;
        bottom: 50px;
    }
}

@media (max-width: 991px) {
    .full-screen {
        padding-bottom: 4rem;
    }
    .color-mode {
        display: none;
    }
    .about-image {
        margin-top: 4em;
    }
    .mobile-mt-2,
    .contact-form {
        margin-top: 2em;
    }
    .contact-info {
        padding: 0 2rem;
    }
}

@media (max-width: 767px) {
    h1 {
        font-size: 2.4em;
    }
    h2 {
        font-size: 2em;
    }
    .animated-item {
        font-size: 28px;
    }
    .navbar-collapse {
        background: #ffffff;
        text-align: center;
        padding-bottom: 20px;
    }
    .navbar-expand-sm .navbar-nav .nav-link {
        padding: 3px 20px;
    }
}

@media (max-width: 580px) {
    .animated-info {
        min-width: 200px;
    }
    .animated-item {
        font-size: 30px;
    }
    .custom-btn-group {
        text-align: center;
    }
    .custom-btn {
        display: block;
        margin: 10px 0;
    }
    .owl-theme .owl-nav {
        display: none;
    }
    .timeline-info small {
        display: block;
        margin: 10px 0 0 0;
    }
}

@media (max-width: 320px) {
    .animated-text {
        margin-top: 0;
    }
    .about-text {
        text-align: center;
    }
    .full-screen {
        padding: 4rem 0;
    }
    .mobile-block {
        display: block;
    }
    .contact-info {
        flex-direction: column;
    }
    .social-links li {
        display: inline-block;
        vertical-align: top;
    }
}

/* ========================================
   MICROINTERACTIONS
   ======================================== */

/* Scroll Progress Indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #ffc200, #ff6b6b, #4ecdc4);
    z-index: 9999;
    transition: width 0.1s ease;
}

/* Enhanced Navigation Microinteractions */
.nav-link {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ffc200, #ff6b6b);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link:hover {
    transform: translateY(-2px);
}

/* Button Ripple Effect */
.custom-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.custom-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.custom-btn:hover::before {
    width: 300px;
    height: 300px;
}

.custom-btn:active {
    transform: scale(0.95);
}

/* Icon Microinteractions */
.icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.icon:hover {
    transform: scale(1.2) rotate(15deg);
    filter: brightness(1.3) saturate(1.2);
}

.icon:active {
    transform: scale(1.1) rotate(10deg);
}

/* Social Links Microinteractions */
.social-links .uil {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.social-links .uil::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 194, 0, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: -1;
}

.social-links .uil:hover::before {
    width: 40px;
    height: 40px;
}

.social-links .uil:hover {
    transform: translateY(-3px) scale(1.2);
}

/* Input Field Microinteractions */
.form-control {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.form-control:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(255, 194, 0, 0.2);
}

.form-control:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Card Hover Microinteractions */
.details-container {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.details-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 194, 0, 0.1), transparent);
    transition: left 0.5s ease;
}

.details-container:hover::before {
    left: 100%;
}

.details-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Timeline Microinteractions */
.timeline-wrapper {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-wrapper:hover {
    transform: translateX(10px);
}

.timeline-yr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-wrapper:hover .timeline-yr {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(71, 69, 89, 0.3);
}

/* Image Microinteractions */
.about-image img {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: brightness(1) contrast(1);
}

.about-image:hover img {
    transform: scale(1.05) rotate(2deg);
    filter: brightness(1.1) contrast(1.1);
}

/* Text Selection Microinteraction */
::selection {
    background: rgba(255, 194, 0, 0.3);
    color: #333;
}

::-moz-selection {
    background: rgba(255, 194, 0, 0.3);
    color: #333;
}

/* Tooltip Microinteractions */
[data-tooltip] {
    position: relative;
    cursor: pointer;
}

[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(-5px);
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

[data-tooltip]::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
    opacity: 1;
    transform: translateX(-50%) translateY(-10px);
}

/* Loading Dots Microinteraction */
.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Shake Animation for Errors */
.shake {
    animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Bounce Microinteraction */
.bounce-on-hover:hover {
    animation: bounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Color Mode Toggle Microinteraction */
.color-mode {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.color-mode:hover {
    transform: scale(1.1);
}

.color-mode:active {
    transform: scale(0.95);
}

/* Navbar Brand Microinteraction */
.navbar-brand {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-brand:hover {
    transform: scale(1.05);
}

/* Skill Item Microinteractions */
.skill {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.skill::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ffc200, #ff6b6b);
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.skill:hover::after {
    width: 100%;
}

/* Animated Counter Microinteraction */
.counter {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.counter:hover {
    transform: scale(1.1);
    color: #ffc200;
}

/* Focus Microinteractions */
button:focus,
.custom-btn:focus {
    outline: 2px solid #ffc200;
    outline-offset: 2px;
}

/* Hover Glow Effect */
.glow-on-hover:hover {
    box-shadow: 0 0 20px rgba(255, 194, 0, 0.6);
    transition: box-shadow 0.3s ease;
}

/* Typing Cursor Animation */
.typing-cursor::after {
    content: '|';
    animation: blink 1s infinite;
    color: #ffc200;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Form Focus States */
.form-focused .form-control {
    border-color: #ffc200 !important;
    box-shadow: 0 0 0 3px rgba(255, 194, 0, 0.2);
}

/* Active Navigation State */
.nav-link.active {
    color: #ffc200 !important;
    font-weight: bold;
}

.nav-link.active::after {
    width: 100% !important;
}

/* Smooth Transitions for All Interactive Elements */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

/* Disable transitions during page load */
.preload * {
    transition: none !important;
}

/* Enhanced Mobile Microinteractions */
@media (max-width: 768px) {
    .bounce-on-hover:hover {
        animation: none;
        transform: scale(1.05);
    }

    .glow-on-hover:hover {
        box-shadow: 0 0 15px rgba(255, 194, 0, 0.4);
    }

    [data-tooltip]:hover::before,
    [data-tooltip]:hover::after {
        display: none;
    }
}

/* ========================================
   SVG IMAGE ANIMATIONS
   ======================================== */

/* Main SVG Container Animation */
.about-image.svg {
    position: relative;
    overflow: visible;
}

.about-image.svg img {
    animation: float-main 6s ease-in-out infinite;
    transform-origin: center center;
    filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@keyframes float-main {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-10px) rotate(1deg);
    }
    50% {
        transform: translateY(-5px) rotate(0deg);
    }
    75% {
        transform: translateY(-15px) rotate(-1deg);
    }
}

/* Animated Background Elements */
.about-image.svg::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle at 30% 70%, rgba(255, 194, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(71, 69, 89, 0.1) 0%, transparent 50%);
    border-radius: 50%;
    animation: rotate-bg 20s linear infinite;
    z-index: -1;
}

@keyframes rotate-bg {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Floating Code Elements */
.about-image.svg::after {
    content: '{ } < /> 01 10';
    position: absolute;
    top: 10%;
    left: -10%;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: rgba(255, 194, 0, 0.6);
    animation: float-code 8s ease-in-out infinite;
    white-space: pre;
    letter-spacing: 2px;
    z-index: 1;
}

@keyframes float-code {
    0%, 100% {
        transform: translateY(0px) translateX(0px);
        opacity: 0.6;
    }
    25% {
        transform: translateY(-20px) translateX(10px);
        opacity: 0.8;
    }
    50% {
        transform: translateY(-10px) translateX(-5px);
        opacity: 0.4;
    }
    75% {
        transform: translateY(-30px) translateX(15px);
        opacity: 0.9;
    }
}

/* Pulsing Glow Effect */
.about-image.svg {
    animation: pulse-glow-image 4s ease-in-out infinite alternate;
}

@keyframes pulse-glow-image {
    from {
        filter: drop-shadow(0 0 10px rgba(255, 194, 0, 0.3));
    }
    to {
        filter: drop-shadow(0 0 25px rgba(255, 194, 0, 0.6));
    }
}

/* Hover Interaction Enhancement */
.about-image.svg:hover img {
    animation: hover-dance 2s ease-in-out infinite;
    transform: scale(1.05);
}

@keyframes hover-dance {
    0%, 100% {
        transform: scale(1.05) translateY(0px) rotate(0deg);
    }
    25% {
        transform: scale(1.08) translateY(-5px) rotate(2deg);
    }
    50% {
        transform: scale(1.06) translateY(-8px) rotate(0deg);
    }
    75% {
        transform: scale(1.07) translateY(-3px) rotate(-2deg);
    }
}

/* Particle Trail Effect */
.svg-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.svg-particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #ffc200;
    border-radius: 50%;
    animation: svg-particle-float 6s ease-in-out infinite;
}

.svg-particle:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
    background: rgba(255, 194, 0, 0.8);
}

.svg-particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 2s;
    background: rgba(71, 69, 89, 0.6);
}

.svg-particle:nth-child(3) {
    top: 80%;
    left: 20%;
    animation-delay: 4s;
    background: rgba(255, 194, 0, 0.4);
}

@keyframes svg-particle-float {
    0%, 100% {
        transform: translateY(0px) translateX(0px) scale(1);
        opacity: 0.8;
    }
    25% {
        transform: translateY(-20px) translateX(10px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-40px) translateX(-10px) scale(0.8);
        opacity: 0.6;
    }
    75% {
        transform: translateY(-20px) translateX(20px) scale(1.1);
        opacity: 0.9;
    }
}

/* Screen Glow Animation */
.about-image.svg {
    position: relative;
}

.screen-glow {
    position: absolute;
    top: 30%;
    left: 35%;
    width: 30%;
    height: 20%;
    background: radial-gradient(ellipse, rgba(0, 123, 255, 0.3) 0%, transparent 70%);
    border-radius: 10px;
    animation: screen-flicker 3s ease-in-out infinite;
    pointer-events: none;
}

@keyframes screen-flicker {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 0.9;
        transform: scale(1.1);
    }
}

/* SVG Entrance Animation */
@keyframes svg-entrance {
    0% {
        opacity: 0;
        transform: translateY(50px) scale(0.8) rotate(-10deg);
    }
    50% {
        opacity: 0.7;
        transform: translateY(-10px) scale(1.1) rotate(5deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0px) scale(1) rotate(0deg);
    }
}

/* Floating Code Elements */
@keyframes float-code-element {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-15px) translateX(8px) rotate(2deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-25px) translateX(-5px) rotate(-1deg);
        opacity: 0.5;
    }
    75% {
        transform: translateY(-10px) translateX(12px) rotate(3deg);
        opacity: 0.8;
    }
}

/* SVG Typing Effect */
@keyframes svg-typing {
    0%, 20% {
        opacity: 0;
        transform: scale(0.8);
    }
    30%, 70% {
        opacity: 1;
        transform: scale(1);
    }
    80%, 100% {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Enhanced SVG Container */
.svg-animated {
    position: relative;
    overflow: visible;
}

.svg-animated::before {
    animation-play-state: running;
}

/* Floating Code Elements Styling */
.floating-code-element {
    text-shadow: 0 0 10px rgba(255, 194, 0, 0.5);
    font-size: 10px !important;
    font-weight: bold;
    letter-spacing: 1px;
}

/* SVG Typing Text Styling */
.svg-typing-text {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 255, 0, 0.3);
}

/* 3D Perspective Effect */
.about-image.svg img {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
}

/* Enhanced Particle System for SVG */
.svg-particles {
    z-index: 1;
}

.svg-particle {
    box-shadow: 0 0 6px currentColor;
}

.svg-particle:nth-child(4) {
    top: 40%;
    left: 60%;
    animation-delay: 1s;
    background: rgba(0, 123, 255, 0.6);
}

.svg-particle:nth-child(5) {
    top: 70%;
    left: 40%;
    animation-delay: 3s;
    background: rgba(255, 194, 0, 0.9);
}

/* Responsive SVG Animations */
@media (max-width: 768px) {
    .about-image.svg img {
        animation-duration: 8s;
    }

    .floating-code-element {
        font-size: 8px !important;
    }

    .svg-typing-text {
        font-size: 6px !important;
        padding: 1px 2px;
    }

    .screen-glow {
        width: 25%;
        height: 15%;
    }
}

/* Dark Mode SVG Adjustments */
.dark-mode .floating-code-element {
    color: rgba(255, 194, 0, 0.9);
    text-shadow: 0 0 15px rgba(255, 194, 0, 0.7);
}

.dark-mode .svg-typing-text {
    background: rgba(0, 0, 0, 0.9);
    border-color: rgba(0, 255, 0, 0.5);
}