/*
Professional Data Analyst Portfolio
Enhanced with Modern Animations & Interactions
*/

@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

/* CSS Custom Properties for Professional Design */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;

    /* Accent Colors */
    --accent-color: #f59e0b;
    --accent-dark: #d97706;
    --accent-light: #fbbf24;

    /* Neutral Colors */
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --background: #ffffff;
    --background-secondary: #f9fafb;
    --background-dark: #111827;
    --border-color: #e5e7eb;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Dark Mode Variables */
[data-theme="dark"] {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --background: #111827;
    --background-secondary: #1f2937;
    --border-color: #374151;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background);
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Professional Animation Keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        border-color: transparent;
    }
    51%, 100% {
        border-color: var(--primary-color);
    }
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

.scale-in {
    animation: scaleIn 0.6s ease-out forwards;
}

/* Professional Layout Classes */
.section {
    padding: var(--spacing-3xl) 0;
    position: relative;
}

.section-hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, var(--background) 0%, var(--background-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.section-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    z-index: -1;
}

.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 600;
}

p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.text-large {
    font-size: 1.25rem;
    font-weight: 500;
}

.text-small {
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Professional Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
}

a:hover {
    color: var(--primary-dark);
    transform: translateY(-1px);
}

a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transition: width var(--transition-normal);
}

a:hover::after {
    width: 100%;
}

/* Professional Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-md) 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm) 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.navbar-brand:hover {
    color: var(--primary-color);
    transform: scale(1.05);
}

.navbar-brand .logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.2rem;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-link {
    font-weight: 500;
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left var(--transition-slow);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    transform: translateY(-2px);
}

.nav-link.active {
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
}

/* Theme Toggle */
.theme-toggle {
    background: var(--background-secondary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.theme-toggle-icon {
    width: 20px;
    height: 20px;
    transition: transform var(--transition-normal);
}

.theme-toggle:hover .theme-toggle-icon {
    transform: rotate(180deg);
}

/* Mobile Navigation */
.mobile-nav-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
}

.mobile-nav-toggle:hover {
    background: var(--background-secondary);
}

.hamburger {
    width: 24px;
    height: 24px;
    position: relative;
}

.hamburger span {
    display: block;
    width: 100%;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all var(--transition-normal);
    position: absolute;
}

.hamburger span:nth-child(1) {
    top: 0;
}

.hamburger span:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
}

.hamburger span:nth-child(3) {
    bottom: 0;
}

.mobile-nav-toggle.active .hamburger span:nth-child(1) {
    transform: rotate(45deg);
    top: 50%;
}

.mobile-nav-toggle.active .hamburger span:nth-child(2) {
    opacity: 0;
}

.mobile-nav-toggle.active .hamburger span:nth-child(3) {
    transform: rotate(-45deg);
    bottom: 50%;
}

@media (max-width: 768px) {
    .mobile-nav-toggle {
        display: block;
    }

    .navbar-nav {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        border-top: 1px solid var(--border-color);
        flex-direction: column;
        padding: var(--spacing-lg);
        gap: var(--spacing-md);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .navbar-nav.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-link {
        width: 100%;
        text-align: center;
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }
}

/* Professional Button Components */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: white;
}

.btn-secondary {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-accent {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-accent:hover {
    background: linear-gradient(135deg, var(--accent-dark), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-large {
    padding: var(--spacing-lg) var(--spacing-2xl);
    font-size: 1.125rem;
}

.btn-small {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
}

.btn-icon {
    width: 20px;
    height: 20px;
    transition: transform var(--transition-normal);
}

.btn:hover .btn-icon {
    transform: translateX(2px);
}

/* Professional Card Components */
.card {
    background: var(--background);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.card:hover::before {
    transform: scaleX(1);
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.card-header {
    margin-bottom: var(--spacing-lg);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.card-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.card-content {
    margin-bottom: var(--spacing-lg);
}

.card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-md);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

/* Skill Components */
.skill-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    margin-bottom: var(--spacing-md);
}

.skill-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(8px);
}

.skill-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
}

.skill-info {
    flex: 1;
}

.skill-name {
    font-weight: 600;
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
}

.skill-level {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.skill-progress {
    width: 100%;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
    margin-top: var(--spacing-sm);
}

.skill-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 3px;
    transition: width 1s ease-out;
    transform-origin: left;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hero Section Styles */
.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    min-height: 80vh;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--background-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-title {
    margin-bottom: var(--spacing-xl);
}

.hero-greeting {
    display: block;
    font-size: 1.5rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.hero-name {
    display: block;
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-sm);
}

.hero-role {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.hero-image-bg {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-xl);
    opacity: 0.1;
    z-index: -1;
}

.hero-image {
    width: 100%;
    height: auto;
    border-radius: var(--radius-xl);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    animation: float 3s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.floating-element:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.floating-element:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 1.5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-xl);
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: var(--text-muted);
    font-size: 0.875rem;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 24px;
    height: 24px;
    margin: 0 auto var(--spacing-sm);
    color: var(--primary-color);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Section Header Styles */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-header p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section Styles */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-intro h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.about-intro p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
}

.about-highlights {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.highlight-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--background-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.highlight-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.highlight-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.highlight-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.highlight-content p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
}

.about-image {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.about-img {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.tech-badge {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    border: 1px solid rgba(37, 99, 235, 0.2);
    animation: fadeInRight 0.6s ease-out forwards;
}

.tech-badge:nth-child(1) { animation-delay: 0.2s; }
.tech-badge:nth-child(2) { animation-delay: 0.4s; }
.tech-badge:nth-child(3) { animation-delay: 0.6s; }
.tech-badge:nth-child(4) { animation-delay: 0.8s; }

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .about-image {
        order: -1;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .section {
        padding: var(--spacing-2xl) 0;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        justify-content: center;
        max-width: 300px;
    }

    .highlight-item {
        flex-direction: column;
        text-align: center;
    }

    .image-overlay {
        position: static;
        flex-direction: row;
        justify-content: center;
        margin-top: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .hero-name {
        font-size: 2.5rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .floating-elements {
        display: none;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-indicator {
        animation: none;
    }

    .floating-element {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-primary: #000000;
        --text-secondary: #000000;
        --background: #ffffff;
        --border-color: #000000;
    }
}

/* Print styles */
@media print {
    .navbar,
    .loading-screen,
    .scroll-indicator,
    .floating-elements {
        display: none !important;
    }

    .section {
        page-break-inside: avoid;
        padding: 1rem 0;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

.animated {
    position: relative;
}

.animated-info {
    display: inline-block;
    vertical-align: top;
    margin-top: 5px;
    min-width: 260px;
    position: relative;
}

.animated-item {
    color: #ffc200;
}

.animated-item {
    font-size: 38px;
    line-height: inherit;
    display: block;
    opacity: 0;
    overflow: hidden;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    animation: BottomTotop 4s linear infinite 0s;
}

.animated-item:nth-child(2n+2) {
    animation-delay: 2s;
}

.animated-item:nth-child(3n+3) {
    animation-delay: 4s;
}

@keyframes BottomTotop {
    0% {
        opacity: 0;
    }
    5% {
        opacity: 0;
        transform: translateY(5px);
    }
    10% {
        opacity: 1;
        transform: translateY(0px);
    }
    25% {
        opacity: 1;
        transform: translateY(0px);
    }
    30% {
        opacity: 0;
        transform: translateY(5px);
    }
    80% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}

.navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 999999;
    will-change: transform;
    transition: transform 200ms linear;
}

.navbar[class*="-unpinned"] {
    transform: translate(0, -150%);
}

.navbar[class*="-pinned"] {
    transform: translate(0, 0);
}

.navbar[class*="headroom--not-top"] {
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    padding-top: 0;
    padding-bottom: 0;
}

.navbar-brand {
    font-weight: bold;
}

.navbar-expand-sm .navbar-nav .nav-link {
    padding: 0 20px;
}

.nav-link {
    font-weight: bold;
    font-size: 16px;
    overflow: hidden;
}

.nav-link span {
    position: relative;
    display: inline-block;
    transition: transform 0.3s;
}

.nav-link span:before {
    position: absolute;
    top: 100%;
    content: attr(data-hover);
    transform: translate3d(0, 0, 0);
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
    color: #ffc200;
    font-weight: bold;
}

.navbar-light .navbar-nav .nav-link:focus span,
.navbar-light .navbar-nav .nav-link:hover span {
    transform: translateY(-100%);
}

.navbar-light .navbar-toggler-icon {
    background: none;
}

.navbar-toggler {
    border: 0;
    padding: 0;
    width: 32px;
    height: 32px;
    line-height: 32px;
    outline: none;
    cursor: pointer;
    margin-right: 10px;
}

.navbar-toggler:focus {
    outline: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:first-child {
    transform: rotate(45deg);
    top: 6px;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:nth-child(2) {
    display: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:last-child {
    transform: rotate(-45deg);
    bottom: 1px;
}

.navbar-toggler .navbar-toggler-icon {
    background: #212121;
    display: block;
    width: 100%;
    height: 2px;
    margin: 5px 0;
    transition: all 0.4s ease;
    position: relative;
}

.copyright-text {
    font-size: 16px;
    font-weight: normal;
    display: block;
}

.color-mode {
    font-weight: bold;
    cursor: pointer;
}

.color-mode-icon {
    position: relative;
    right: 6px;
}

.color-mode-icon:after {
    font-family: 'unicons';
    content: '\ea9f';
    font-size: 30px;
    font-weight: 300;
    font-style: normal;
}

.color-mode-icon.active:after {
    font-family: 'unicons';
    content: '\eb65';
    font-size: 30px;
    color: #ffffff;
}

.dark-mode {
    background: #0c0c0d;
}

.dark-mode .navbar-light .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
}

.dark-mode .navbar-light .navbar-nav .nav-link:hover {
    color: #ffc200;
}

.dark-mode .navbar[class*="headroom--not-top"] {
    background: #0c0c0d;
    border-bottom: 1px solid #1f1f1f;
}

.dark-mode .small-text {
    background: #0d0c15;
    color: #ffffff;
}

.dark-mode .feature-card .uil,
.dark-mode .navbar-light .navbar-brand,
.dark-mode h1,
.dark-mode h2,
.dark-mode h3,
.dark-mode h4,
.dark-mode h5,
.dark-mode h6,
.dark-mode .color-mode {
    color: #ffffff;
}

.dark-mode .owl-carousel .owl-nav button.owl-next,
.dark-mode .owl-carousel .owl-nav button.owl-prev,
.dark-mode .owl-carousel button.owl-dot {
    color: #ffffff;
}

.timeline-wrapper {
    position: relative;
    padding: 22px 0;
}

.timeline-wrapper:last-child:before {
    height: 0;
}

.timeline-wrapper:before {
    content: "";
    background: #474559;
    width: 3px;
    height: 100%;
    position: absolute;
    left: 38px;
}

.timeline-yr {
    background: #474559;
    border-radius: 100%;
    position: absolute;
    width: 75px;
    height: 75px;
    line-height: 75px;
    text-align: center;
}

.timeline-yr span {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    display: block;
    line-height: 75px;
}

.timeline-info {
    display: inline-block;
    vertical-align: top;
    max-width: 432px;
    margin-top: 5em;
    margin-left: 6em;
}

.timeline-info small {
    color: #474559;
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
}

.owl-carousel .owl-nav span {
    display: none;
}

.owl-carousel .owl-nav .owl-prev,
.owl-carousel .owl-nav .owl-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: white;  /* Background color for arrows */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

/* Left Arrow */
.owl-carousel .owl-nav .owl-prev {
    left: -60px; /* Adjust position */
}

.owl-carousel .owl-nav .owl-next {
    right: -60px; /* Adjust position */
}

/* Arrow Icons */
.owl-carousel .owl-nav .owl-prev:before,
.owl-carousel .owl-nav .owl-next:before {
    content: "‹"; /* Unicode for left arrow */
    font-size: 30px;
    color: black; /* Arrow color */
    font-family: Arial, sans-serif; /* Change to a standard font */
}

/* Right Arrow */
.owl-carousel .owl-nav .owl-next:before {
    content: "›"; /* Unicode for right arrow */
}


.owl-theme .owl-nav [class*=owl-] {
    background: none;
    border-radius: 0;
    margin: 0;
    padding: 0;
    font-size: inherit;
}

.owl-theme .owl-nav [class*=owl-]:hover {
    color: #ffc200;
}

.owl-theme .owl-dots .owl-dot {
    outline: none;
}

.owl-theme .owl-dots .owl-dot span {
    width: 8px;
    height: 8px;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
    background: #5b5b5b;
}

.google-map iframe {
    width: 100%;
}

#contact {
    width: 80%;
    margin: auto;
}

.contact-form {
    position: relative;
}

.contact-form .form-control {
    background: transparent;
    border-radius: 2px;
    outline: none;
    box-shadow: none;
    font-weight: bold;
    margin: 16px 0;
}

.contact-form .form-control:not(textarea) {
    height: 48px;
}

.contact-form .form-control:hover,
.contact-form .form-control:focus {
    border-color: #ffc200;
}

.contact-form .submit-btn {
    background: #ffc200;
    border-radius: 50px;
    color: #ffffff;
    font-weight: bold;
    border: 0;
    cursor: pointer;
    transition: all 0.4s ease;
}

.contact-form .submit-btn:hover {
    background: #474559;
}

.contact-info {
    background: #474559;
    border-radius: 0 0 3px 3px;
    position: relative;
    bottom: 8px;
}

.contact-info p,
.contact-info a {
    color: #f7f3f3;
}

.social-links .uil {
    color: #f7f3f3;
    font-size: 20px;
    display: block;
    margin: 5px 0;
}

.social-links .uil:hover {
    color: #ffc200;
}

.icon {
    cursor: pointer;
    height: 2rem;
}

.contact-icon {
    cursor: default;
}

.email-icon {
    height: 2.5rem;
}

.section__text__p1 {
    text-align: center;
}

.title {
    font-size: 3rem;
    text-align: center;
}

.contact-info-upper-container {
    display: flex;
    justify-content: space-evenly;
    border-radius: 2rem;
    border: rgb(53, 53, 53) 0.1rem solid;
    border-color: rgb(163, 163, 163);
    background: (250, 250, 250);
    margin: 2rem 20%;
    padding: 0.5rem;
}

.about-containers {
    gap: 2rem;
    margin: auto;
    margin-bottom: 2rem;
    margin-top: 2rem;
    max-width: 80%;
}

article {
    display: flex;
    width: 10rem;
    justify-content: space-around;
    gap: 0.5rem;
}

article .icon {
    cursor: default;
}

.article-container {
    display: flex;
    text-align: initial;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 2.5rem;
    justify-content: space-around;
}

.details-container {
    padding: 1.5rem;
    flex: 1;
    background: white;
    border-radius: 2rem;
    border: rgb(53, 53, 53) 0.1rem solid;
    border-color: rgb(163, 163, 163);
    text-align: center;
}

.about-details-container {
    justify-content: center;
    flex-direction: column;
}

.experience-sub-title {
    color: rgb(85, 85, 85);
    font-weight: 600;
    font-size: 1.75rem;
    margin-bottom: 2rem;
}

#experience {
    position: relative;
    margin-top: 5%;
}

.about-containers,
.about-details-container {
    display: flex;
}

.experience-details-container {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.contact-info-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 1rem;
}

a {
    color: black;
    text-decoration: none;
    text-decoration-color: white;
    transition: all 300ms ease;
}

a:hover {
    color: grey;
    text-decoration: underline;
    text-underline-offset: 1rem;
    text-decoration-color: rgb(181, 181, 181);
}

@media (min-width: 1270px) {
    .owl-theme .owl-dots {
        position: relative;
        bottom: 50px;
    }
}

@media (max-width: 991px) {
    .full-screen {
        padding-bottom: 4rem;
    }
    .color-mode {
        display: none;
    }
    .about-image {
        margin-top: 4em;
    }
    .mobile-mt-2,
    .contact-form {
        margin-top: 2em;
    }
    .contact-info {
        padding: 0 2rem;
    }
}

@media (max-width: 767px) {
    h1 {
        font-size: 2.4em;
    }
    h2 {
        font-size: 2em;
    }
    .animated-item {
        font-size: 28px;
    }
    .navbar-collapse {
        background: #ffffff;
        text-align: center;
        padding-bottom: 20px;
    }
    .navbar-expand-sm .navbar-nav .nav-link {
        padding: 3px 20px;
    }
}

@media (max-width: 580px) {
    .animated-info {
        min-width: 200px;
    }
    .animated-item {
        font-size: 30px;
    }
    .custom-btn-group {
        text-align: center;
    }
    .custom-btn {
        display: block;
        margin: 10px 0;
    }
    .owl-theme .owl-nav {
        display: none;
    }
    .timeline-info small {
        display: block;
        margin: 10px 0 0 0;
    }
}

@media (max-width: 320px) {
    .animated-text {
        margin-top: 0;
    }
    .about-text {
        text-align: center;
    }
    .full-screen {
        padding: 4rem 0;
    }
    .mobile-block {
        display: block;
    }
    .contact-info {
        flex-direction: column;
    }
    .social-links li {
        display: inline-block;
        vertical-align: top;
    }
}