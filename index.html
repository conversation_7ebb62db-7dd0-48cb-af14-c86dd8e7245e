<!DOCTYPE html>
<html lang="en" data-theme="light">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Bebin FM - Professional Data Analyst specializing in Python, SQL, Power BI, and Tableau. Transforming raw data into actionable business insights with advanced analytics and visualization.">
    <meta name="keywords" content="Data Analyst, Python, SQL, Power BI, Tableau, Data Visualization, Business Intelligence, Analytics, Data Science, Machine Learning">
    <meta name="author" content="Bebin FM">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Bebin FM - Professional Data Analyst Portfolio">
    <meta property="og:description" content="Transforming raw data into actionable business insights with advanced analytics and visualization expertise.">
    <meta property="og:image" content="images/project/Fotoram.io.png">
    <meta property="og:url" content="https://bebinfm.github.io">
    <meta property="og:type" content="website">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Bebin FM - Professional Data Analyst">
    <meta name="twitter:description" content="Professional Data Analyst specializing in Python, SQL, Power BI, and advanced analytics.">
    <meta name="twitter:image" content="images/project/Fotoram.io.png">

    <title>Bebin FM - Professional Data Analyst Portfolio</title>
    <link rel="icon" href="images/project/Fotoram.io.png" type="image/png">
    <link rel="apple-touch-icon" href="images/project/Fotoram.io.png">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/tooplate-style.css" as="style">
    <link rel="preload" href="js/custom.js" as="script">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/owl.carousel.min.css">
    <link rel="stylesheet" href="css/owl.theme.default.min.css">
    <link rel="stylesheet" href="css/tooplate-style.css">
    <link rel="stylesheet" href="css/mediaQuries.css">

    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Bebin FM",
        "jobTitle": "Data Analyst",
        "description": "Professional Data Analyst specializing in Python, SQL, Power BI, and advanced analytics",
        "url": "https://bebinfm.github.io",
        "sameAs": [
            "https://www.linkedin.com/in/bebin-fm-b89028258",
            "https://github.com/Bebinfm"
        ],
        "knowsAbout": ["Data Analysis", "Python", "SQL", "Power BI", "Tableau", "Data Visualization"],
        "alumniOf": {
            "@type": "EducationalOrganization",
            "name": "Excel Engineering College"
        }
    }
    </script>
</head>

<body>

    <!-- Professional Navigation -->
    <nav class="navbar" id="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <a href="#home" class="navbar-brand" aria-label="Bebin FM - Data Analyst">
                <div class="logo-icon">BF</div>
                <span>Bebin FM</span>
            </a>

            <button class="mobile-nav-toggle" id="mobile-nav-toggle" aria-label="Toggle mobile navigation" aria-expanded="false">
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </button>

            <ul class="navbar-nav" id="navbar-nav" role="menubar">
                <li role="none">
                    <a href="#home" class="nav-link active" role="menuitem" data-section="home">
                        <span>Home</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#about" class="nav-link" role="menuitem" data-section="about">
                        <span>About</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#experience" class="nav-link" role="menuitem" data-section="experience">
                        <span>Experience</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#skills" class="nav-link" role="menuitem" data-section="skills">
                        <span>Skills</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#projects" class="nav-link" role="menuitem" data-section="projects">
                        <span>Projects</span>
                    </a>
                </li>
                <li role="none">
                    <a href="#contact" class="nav-link" role="menuitem" data-section="contact">
                        <span>Contact</span>
                    </a>
                </li>
                <li role="none">
                    <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                        <svg class="theme-toggle-icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Theme</span>
                    </button>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="section-hero" id="home" aria-label="Hero section">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text animate-on-scroll">
                    <div class="hero-badge">
                        <span class="badge-icon">👋</span>
                        <span>Welcome to my portfolio</span>
                    </div>

                    <h1 class="hero-title">
                        <span class="hero-greeting">Hi, I'm</span>
                        <span class="hero-name typing-animation">Bebin FM</span>
                        <span class="hero-role">Professional Data Analyst</span>
                    </h1>

                    <p class="hero-description">
                        Transforming raw data into actionable insights that drive smarter business decisions.
                        Specialized in Python, SQL, Power BI, and advanced analytics to unlock the power of your data.
                    </p>

                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number" data-count="50">0</span>
                            <span class="stat-label">Projects Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-count="2">0</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" data-count="5">0</span>
                            <span class="stat-label">Technologies Mastered</span>
                        </div>
                    </div>

                    <div class="hero-actions">
                        <a href="https://drive.google.com/file/d/1ore61oarBJh_21LWVYU56G8-mHKymq0j/view?usp=sharing"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="btn btn-primary btn-large"
                           aria-label="Download resume PDF">
                            <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            Download Resume
                        </a>

                        <a href="#contact" class="btn btn-outline btn-large">
                            <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                            </svg>
                            Get In Touch
                        </a>

                        <a href="https://github.com/Bebinfm"
                           target="_blank"
                           rel="noopener noreferrer"
                           class="btn btn-secondary"
                           aria-label="Visit GitHub profile">
                            <svg class="btn-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                            </svg>
                            GitHub
                        </a>
                    </div>
                </div>

                <div class="hero-visual animate-on-scroll">
                    <div class="hero-image-container">
                        <div class="hero-image-bg"></div>
                        <img src="images/undraw/undraw_software_engineer_lvl5.svg"
                             alt="Data analyst working with charts and analytics"
                             class="hero-image"
                             loading="eager">
                        <div class="floating-elements">
                            <div class="floating-element" data-speed="2">📊</div>
                            <div class="floating-element" data-speed="3">📈</div>
                            <div class="floating-element" data-speed="1.5">💡</div>
                            <div class="floating-element" data-speed="2.5">🔍</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="scroll-indicator">
                <div class="scroll-arrow">
                    <svg fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <span>Scroll to explore</span>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="about" aria-label="About section">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <h2>About Me</h2>
                <p>Get to know more about my background, passion, and expertise in data analytics</p>
            </div>

            <div class="about-content">
                <div class="about-text animate-on-scroll">
                    <div class="about-intro">
                        <h3>Passionate Data Analyst</h3>
                        <p>
                            I'm a dedicated data analyst with a strong foundation in statistical analysis,
                            data visualization, and business intelligence. My journey in data analytics began
                            during my studies in Artificial Intelligence and Data Science, where I discovered
                            my passion for uncovering insights hidden within complex datasets.
                        </p>
                    </div>

                    <div class="about-highlights">
                        <div class="highlight-item">
                            <div class="highlight-icon">🎯</div>
                            <div class="highlight-content">
                                <h4>Problem Solver</h4>
                                <p>I excel at identifying business challenges and developing data-driven solutions that deliver measurable results.</p>
                            </div>
                        </div>

                        <div class="highlight-item">
                            <div class="highlight-icon">📊</div>
                            <div class="highlight-content">
                                <h4>Visualization Expert</h4>
                                <p>Creating compelling dashboards and reports that make complex data accessible to stakeholders at all levels.</p>
                            </div>
                        </div>

                        <div class="highlight-item">
                            <div class="highlight-icon">🚀</div>
                            <div class="highlight-content">
                                <h4>Continuous Learner</h4>
                                <p>Always staying updated with the latest tools and techniques in data analytics and machine learning.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="about-image animate-on-scroll">
                    <div class="image-container">
                        <img src="images/undraw/undraw_software_engineer_lvl5.svg"
                             alt="Professional data analyst workspace"
                             class="about-img">
                        <div class="image-overlay">
                            <div class="tech-badge">Python</div>
                            <div class="tech-badge">SQL</div>
                            <div class="tech-badge">Power BI</div>
                            <div class="tech-badge">Tableau</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- FEATURES -->
    <section class="resume py-5 d-lg-flex justify-content-center align-items-center" id="resume">
        <div class="container">
            <div class="row">

                <div class="col-lg-6 col-12">
                    <h2 class="mb-4">Experiences</h2>

                    <div class="timeline">
                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2025</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span>Data Science Intern</span><small>Alo Info-Tech</small></h3>
                                <p> As a Data Science with Machine Learning Intern, I gained hands-on experience in data preprocessing,
                                    analysis, and visualization using Python.

                                </p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>1 mnth</span>
                            </div>

                        </div>





                    </div>
                </div>

                <div class="col-lg-6 col-12">
                    <h2 class="mb-4 mobile-mt-2">Educations</h2>

                    <div class="timeline">
                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2022</span>
                            </div>
                            <div class="timeline-info">
                                <h3><span>Bachelor of Artical Intelligence and Data Science
                    Application</span></h3>
                                <p>Excel Engineering College, <br /> Namakall, Tamil nadu.</p>
                            </div>
                        </div>

                        <div class="timeline-wrapper">
                            <div class="timeline-yr">
                                <span>2026</span>
                            </div>

                        </div>


                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- Skills -->

    <section id="experience">

        <h1 class="title">My Skills</h1>
        <div class="experience-details-container">
            <div class="about-containers">
                <div class="details-container">
                    <h2 class="experience-sub-title">Technical Skills</h2>
                    <div class="article-container">

                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Advanced Excel</p>
                                <p></p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />

                            <div>
                                <p class="skill">SQL</p>
                                <p>Intermediate</p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Microsoft Power BI</p>
                                <p>Intermediate</p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Python</p>
                                <p>Intermediate</p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Tableau</p>
                                <p>Intermediate</p>
                            </div>
                        </article>
                    </div>
                </div>
                <div class="details-container">
                    <h2 class="experience-sub-title">Other Skills</h2>
                    <div class="article-container">
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Data Cleaning</p>
                                <p>Intermediate</p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />
                            <div>
                                <p class="skill">Data Modeling</p>
                                <p>Experienced</p>
                            </div>
                        </article>
                        <article>
                            <img src="images/project/checkmark.png" alt="Experience icon" class="icon" />

                            <div>
                                <p class="skill">Communication</p>
                            </div>
                        </article>
                    </div>
                </div>
            </div>
        </div>

    </section>

    <!-- PROJECTS -->
    <section class="project py-5" id="project">
        <div class="container">

            <div class="row">
                <div class="col-lg-11 text-center mx-auto col-12">

                    <div class="col-lg-8 mx-auto">
                        <h2>Browse My Recent Projects

                        </h2>
                    </div>

                    <div class="owl-carousel owl-theme">
                        <div class="item">
                            <div class="project-info">
                                <a href="https://app.powerbi.com/view?r=eyJrIjoiZWEzZTlmNmItMTRmZC00NmNjLTg4YzQtMmI5MzU0MDJmZDgwIiwidCI6Ijg3NjUzMjlkLTgxNTMtNDI0Yy1hMDcyLTQzMDI5ODVmNjgwZCJ9"> <img src="images/project/awesome.png" class="img-fluid" alt="project image"></a>
                            </div>
                        </div>

                        <div class="item">
                            <div class="project-info">
                                <a href="https://app.powerbi.com/view?r=eyJrIjoiMzZjZjc3OGUtNjNiNS00NzY5LWIyMzQtNWFkYTBiNmI4MTBkIiwidCI6Ijg3NjUzMjlkLTgxNTMtNDI0Yy1hMDcyLTQzMDI5ODVmNjgwZCJ9"><img src="images/project/Screenshot 2025-01-23 203521.png" class="img-fluid" alt="project image"></a>
                            </div>
                        </div>

                        <div class="item">
                            <div class="project-info">
                                <a href="https://app.powerbi.com/view?r=eyJrIjoiYWNhN2IyN2YtYjQ3OC00M2ExLWJiOTEtNDhiYTI2OGZiY2E5IiwidCI6Ijg3NjUzMjlkLTgxNTMtNDI0Yy1hMDcyLTQzMDI5ODVmNjgwZCJ9"> <img src="images/project/Screenshot 2025-01-31 220644.png" class="img-fluid" alt="project image"></a>
                            </div>
                        </div>
                        </div>


                    </div>

                </div>
            </div>
        </div>
    </section>
    <!-- Contact -->
    <section id="contact">
        <p class="section__text__p1">Get in Touch</p>
        <h1 class="title">Contact Me</h1>
        <div class="contact-info-upper-container">
            <div class="contact-info-container">
                <img src="images/project/email.png" alt="Email icon" class="icon contact-icon " />
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            <div class="contact-info-container">
                <img src="images/project/linkedin.png" alt="LinkedIn icon" class="icon contact-icon" />
                <a href="https://www.linkedin.com/in/bebin-fm-b89028258">LinkedIn</a></p>

            </div>
            <div class="contact-info-container">
                <img src="images/project/github-logo.png" alt="My Github profile" class="icon contact-icon" />
                <a href="https://github.com/Bebinfm">Github</a></p>


            </div>
        </div>
    </section>

    <!-- FOOTER -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">

                <div class="col-lg-12 col-12">
                    <p class="copyright-text text-center">Copyright &copy; 2025. All rights reserved</p>
                    <p class="copyright-text text-center">Designed by Bebin</a>
                    </p>
                </div>

            </div>
        </div>
    </footer>



    <script src="js/jquery-3.3.1.min.js"></script>
    <script src="js/popper.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/Headroom.js"></script>
    <script src="js/jQuery.headroom.js"></script>
    <script src="js/owl.carousel.min.js"></script>
    <script src="js/smoothscroll.js"></script>
    <script src="js/custom.js"></script>

</body>

</html>