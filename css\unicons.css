@font-face {
    font-family: 'unicons';
    src: url('../font/unicons.eot?42479262');
    src: url('../font/unicons.eot?42479262#iefix') format('embedded-opentype'), url('../font/unicons.woff2?42479262') format('woff2'), url('../font/unicons.woff?42479262') format('woff'), url('../font/unicons.ttf?42479262') format('truetype'), url('../font/unicons.svg?42479262#unicons') format('svg');
    font-weight: normal;
    font-style: normal;
}

/* Chrome hack: SVG is rendered more smooth in Windozze. 100% magic, uncomment if you need it. */

/* Note, that will break hinting! In other OS-es font will be not as sharp as it could be */

/*
@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: 'unicons';
    src: url('../font/unicons.svg?42479262#unicons') format('svg');
  }
}
*/

[class^="uil-"]:before, [class*=" uil-"]:before {
    font-family: "unicons";
    font-style: normal;
    font-weight: normal;
    speak: none;
   
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    margin-right: .2em;
    text-align: center;
    /* opacity: .8; */
   
    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;
   
    /* fix buttons height, for twitter bootstrap */
    line-height: 1em;
   
    /* Animation center compensation - margins should be symmetric */
    /* remove if not needed */
    margin-left: .2em;
   
    /* you can be more comfortable with increased icons size */
    /* font-size: 120%; */
   
    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
   
    /* Uncomment for 3D effect */
    /* text-shadow: 1px 1px 1px rgba(127, 127, 127, 0.3); */
  }

.uil-abacus:before {
    content: '\e800';
}

/* '' */

.uil-accessible-icon-alt:before {
    content: '\e801';
}

/* '' */

.uil-adjust-alt:before {
    content: '\e802';
}

/* '' */

.uil-adjust-circle:before {
    content: '\e803';
}

/* '' */

.uil-adjust-half:before {
    content: '\e804';
}

/* '' */

.uil-adjust:before {
    content: '\e805';
}

/* '' */

.uil-airplay:before {
    content: '\e806';
}

/* '' */

.uil-align-alt:before {
    content: '\e807';
}

/* '' */

.uil-align-center-alt:before {
    content: '\e808';
}

/* '' */

.uil-align-center-h:before {
    content: '\e809';
}

/* '' */

.uil-align-center-justify:before {
    content: '\e80a';
}

/* '' */

.uil-align-center-v:before {
    content: '\e80b';
}

/* '' */

.uil-align-center:before {
    content: '\e80c';
}

/* '' */

.uil-align-justify:before {
    content: '\e80d';
}

/* '' */

.uil-align-left-justify:before {
    content: '\e80e';
}

/* '' */

.uil-align-left:before {
    content: '\e80f';
}

/* '' */

.uil-align-letter-right:before {
    content: '\e810';
}

/* '' */

.uil-align-right-justify:before {
    content: '\e811';
}

/* '' */

.uil-align-right:before {
    content: '\e812';
}

/* '' */

.uil-align:before {
    content: '\e813';
}

/* '' */

.uil-ambulance:before {
    content: '\e814';
}

/* '' */

.uil-analysis:before {
    content: '\e815';
}

/* '' */

.uil-analytics:before {
    content: '\e816';
}

/* '' */

.uil-anchor:before {
    content: '\e817';
}

/* '' */

.uil-android-phone-slash:before {
    content: '\e818';
}

/* '' */

.uil-angle-double-down:before {
    content: '\e819';
}

/* '' */

.uil-angle-double-left:before {
    content: '\e81a';
}

/* '' */

.uil-angle-double-right:before {
    content: '\e81b';
}

/* '' */

.uil-angle-double-up:before {
    content: '\e81c';
}

/* '' */

.uil-angle-down:before {
    content: '\e81d';
}

/* '' */

.uil-angle-left:before {
    content: '\e81e';
}

/* '' */

.uil-angle-right-b:before {
    content: '\e81f';
}

/* '' */

.uil-angle-right:before {
    content: '\e820';
}

/* '' */

.uil-angle-up:before {
    content: '\e821';
}

/* '' */

.uil-annoyed-alt:before {
    content: '\e822';
}

/* '' */

.uil-annoyed:before {
    content: '\e823';
}

/* '' */

.uil-apps:before {
    content: '\e824';
}

/* '' */

.uil-archive-alt:before {
    content: '\e825';
}

/* '' */

.uil-archive:before {
    content: '\e826';
}

/* '' */

.uil-arrow-break:before {
    content: '\e827';
}

/* '' */

.uil-arrow-circle-down:before {
    content: '\e828';
}

/* '' */

.uil-arrow-circle-left:before {
    content: '\e829';
}

/* '' */

.uil-arrow-circle-right:before {
    content: '\e82a';
}

/* '' */

.uil-arrow-circle-up:before {
    content: '\e82b';
}

/* '' */

.uil-arrow-compress-h:before {
    content: '\e82c';
}

/* '' */

.uil-arrow-down-left:before {
    content: '\e82d';
}

/* '' */

.uil-arrow-down-right:before {
    content: '\e82e';
}

/* '' */

.uil-arrow-down:before {
    content: '\e82f';
}

/* '' */

.uil-arrow-from-right:before {
    content: '\e830';
}

/* '' */

.uil-arrow-from-top:before {
    content: '\e831';
}

/* '' */

.uil-arrow-growth:before {
    content: '\e832';
}

/* '' */

.uil-arrow-left:before {
    content: '\e833';
}

/* '' */

.uil-arrow-random:before {
    content: '\e834';
}

/* '' */

.uil-arrow-resize-diagonal:before {
    content: '\e835';
}

/* '' */

.uil-arrow-right:before {
    content: '\e836';
}

/* '' */

.uil-arrow-to-bottom:before {
    content: '\e837';
}

/* '' */

.uil-arrow-to-right:before {
    content: '\e838';
}

/* '' */

.uil-arrow-up-left:before {
    content: '\e839';
}

/* '' */

.uil-arrow-up-right:before {
    content: '\e83a';
}

/* '' */

.uil-arrow-up:before {
    content: '\e83b';
}

/* '' */

.uil-arrows-h-alt:before {
    content: '\e83c';
}

/* '' */

.uil-arrows-h:before {
    content: '\e83d';
}

/* '' */

.uil-arrows-left-down:before {
    content: '\e83e';
}

/* '' */

.uil-arrows-maximize:before {
    content: '\e83f';
}

/* '' */

.uil-arrows-merge:before {
    content: '\e840';
}

/* '' */

.uil-arrows-resize-h:before {
    content: '\e841';
}

/* '' */

.uil-arrows-resize-v:before {
    content: '\e842';
}

/* '' */

.uil-arrows-resize:before {
    content: '\e843';
}

/* '' */

.uil-arrows-right-down:before {
    content: '\e844';
}

/* '' */

.uil-arrows-shrink-h:before {
    content: '\e845';
}

/* '' */

.uil-arrows-shrink-v:before {
    content: '\e846';
}

/* '' */

.uil-arrows-up-right:before {
    content: '\e847';
}

/* '' */

.uil-arrows-v-alt:before {
    content: '\e848';
}

/* '' */

.uil-arrows-v:before {
    content: '\e849';
}

/* '' */

.uil-at:before {
    content: '\e84a';
}

/* '' */

.uil-atm-card:before {
    content: '\e84b';
}

/* '' */

.uil-atom:before {
    content: '\e84c';
}

/* '' */

.uil-auto-flash:before {
    content: '\e84d';
}

/* '' */

.uil-award-alt:before {
    content: '\e84e';
}

/* '' */

.uil-award:before {
    content: '\e84f';
}

/* '' */

.uil-baby-carriage:before {
    content: '\e850';
}

/* '' */

.uil-backpack:before {
    content: '\e851';
}

/* '' */

.uil-backspace:before {
    content: '\e852';
}

/* '' */

.uil-backward:before {
    content: '\e853';
}

/* '' */

.uil-bag-alt:before {
    content: '\e854';
}

/* '' */

.uil-bag-slash:before {
    content: '\e855';
}

/* '' */

.uil-bag:before {
    content: '\e856';
}

/* '' */

.uil-ball:before {
    content: '\e857';
}

/* '' */

.uil-ban:before {
    content: '\e858';
}

/* '' */

.uil-bars:before {
    content: '\e859';
}

/* '' */

.uil-basketball-hoop:before {
    content: '\e85a';
}

/* '' */

.uil-basketball:before {
    content: '\e85b';
}

/* '' */

.uil-battery-bolt:before {
    content: '\e85c';
}

/* '' */

.uil-battery-empty:before {
    content: '\e85d';
}

/* '' */

.uil-bed-double:before {
    content: '\e85e';
}

/* '' */

.uil-bed:before {
    content: '\e85f';
}

/* '' */

.uil-bell-school:before {
    content: '\e860';
}

/* '' */

.uil-bell-slash:before {
    content: '\e861';
}

/* '' */

.uil-bill:before {
    content: '\e862';
}

/* '' */

.uil-bitcoin-circle:before {
    content: '\e863';
}

/* '' */

.uil-bitcoin:before {
    content: '\e864';
}

/* '' */

.uil-bluetooth-b:before {
    content: '\e865';
}

/* '' */

.uil-bolt-alt:before {
    content: '\e866';
}

/* '' */

.uil-bolt-slash:before {
    content: '\e867';
}

/* '' */

.uil-bolt:before {
    content: '\e868';
}

/* '' */

.uil-book-alt:before {
    content: '\e869';
}

/* '' */

.uil-book-medical:before {
    content: '\e86a';
}

/* '' */

.uil-book-open:before {
    content: '\e86b';
}

/* '' */

.uil-book-reader:before {
    content: '\e86c';
}

/* '' */

.uil-book:before {
    content: '\e86d';
}

/* '' */

.uil-bookmark-full:before {
    content: '\e86e';
}

/* '' */

.uil-bookmark:before {
    content: '\e86f';
}

/* '' */

.uil-books:before {
    content: '\e870';
}

/* '' */

.uil-boombox:before {
    content: '\e871';
}

/* '' */

.uil-border-alt:before {
    content: '\e872';
}

/* '' */

.uil-border-bottom:before {
    content: '\e873';
}

/* '' */

.uil-border-clear:before {
    content: '\e874';
}

/* '' */

.uil-border-horizontal:before {
    content: '\e875';
}

/* '' */

.uil-border-inner:before {
    content: '\e876';
}

/* '' */

.uil-border-left:before {
    content: '\e877';
}

/* '' */

.uil-border-out:before {
    content: '\e878';
}

/* '' */

.uil-border-right:before {
    content: '\e879';
}

/* '' */

.uil-border-top:before {
    content: '\e87a';
}

/* '' */

.uil-border-vertical:before {
    content: '\e87b';
}

/* '' */

.uil-box:before {
    content: '\e87c';
}

/* '' */

.uil-briefcase-alt:before {
    content: '\e87d';
}

/* '' */

.uil-briefcase:before {
    content: '\e87e';
}

/* '' */

.uil-bright:before {
    content: '\e87f';
}

/* '' */

.uil-brightness-empty:before {
    content: '\e880';
}

/* '' */

.uil-brightness-half:before {
    content: '\e881';
}

/* '' */

.uil-brightness-low:before {
    content: '\e882';
}

/* '' */

.uil-brightness-minus:before {
    content: '\e883';
}

/* '' */

.uil-brightness-plus:before {
    content: '\e884';
}

/* '' */

.uil-brightness:before {
    content: '\e885';
}

/* '' */

.uil-bring-bottom:before {
    content: '\e886';
}

/* '' */

.uil-bring-front:before {
    content: '\e887';
}

/* '' */

.uil-brush-alt:before {
    content: '\e888';
}

/* '' */

.uil-bug:before {
    content: '\e889';
}

/* '' */

.uil-building:before {
    content: '\e88a';
}

/* '' */

.uil-bullseye:before {
    content: '\e88b';
}

/* '' */

.uil-bus-alt:before {
    content: '\e88c';
}

/* '' */

.uil-bus-school:before {
    content: '\e88d';
}

/* '' */

.uil-bus:before {
    content: '\e88e';
}

/* '' */

.uil-calcualtor:before {
    content: '\e88f';
}

/* '' */

.uil-calculator-alt:before {
    content: '\e890';
}

/* '' */

.uil-calendar-alt:before {
    content: '\e891';
}

/* '' */

.uil-calendar-slash:before {
    content: '\e892';
}

/* '' */

.uil-calender:before {
    content: '\e893';
}

/* '' */

.uil-calling:before {
    content: '\e894';
}

/* '' */

.uil-camera-plus:before {
    content: '\e895';
}

/* '' */

.uil-camera-slash:before {
    content: '\e896';
}

/* '' */

.uil-camera:before {
    content: '\e897';
}

/* '' */

.uil-cancel:before {
    content: '\e898';
}

/* '' */

.uil-capsule:before {
    content: '\e899';
}

/* '' */

.uil-capture:before {
    content: '\e89a';
}

/* '' */

.uil-car-sideview:before {
    content: '\e89b';
}

/* '' */

.uil-car-slash:before {
    content: '\e89c';
}

/* '' */

.uil-car-wash:before {
    content: '\e89d';
}

/* '' */

.uil-car:before {
    content: '\e89e';
}

/* '' */

.uil-card-atm:before {
    content: '\e89f';
}

/* '' */

.uil-caret-right:before {
    content: '\e8a0';
}

/* '' */

.uil-cart:before {
    content: '\e8a1';
}

/* '' */

.uil-cell:before {
    content: '\e8a2';
}

/* '' */

.uil-celsius:before {
    content: '\e8a3';
}

/* '' */

.uil-chart-bar-alt:before {
    content: '\e8a4';
}

/* '' */

.uil-chart-bar:before {
    content: '\e8a5';
}

/* '' */

.uil-chart-down:before {
    content: '\e8a6';
}

/* '' */

.uil-chart-growth-alt:before {
    content: '\e8a7';
}

/* '' */

.uil-chart-growth:before {
    content: '\e8a8';
}

/* '' */

.uil-chart-line:before {
    content: '\e8a9';
}

/* '' */

.uil-chart-pie-alt:before {
    content: '\e8aa';
}

/* '' */

.uil-chart-pie:before {
    content: '\e8ab';
}

/* '' */

.uil-chart:before {
    content: '\e8ac';
}

/* '' */

.uil-chat-bubble-user:before {
    content: '\e8ad';
}

/* '' */

.uil-chat-info:before {
    content: '\e8ae';
}

/* '' */

.uil-chat:before {
    content: '\e8af';
}

/* '' */

.uil-check-circle:before {
    content: '\e8b0';
}

/* '' */

.uil-check-square:before {
    content: '\e8b1';
}

/* '' */

.uil-check:before {
    content: '\e8b2';
}

/* '' */

.uil-circle-layer:before {
    content: '\e8b3';
}

/* '' */

.uil-circle:before {
    content: '\e8b4';
}

/* '' */

.uil-circuit:before {
    content: '\e8b5';
}

/* '' */

.uil-clapper-board:before {
    content: '\e8b6';
}

/* '' */

.uil-clipboard-alt:before {
    content: '\e8b7';
}

/* '' */

.uil-clipboard-blank:before {
    content: '\e8b8';
}

/* '' */

.uil-clipboard-notes:before {
    content: '\e8b9';
}

/* '' */

.uil-clipboard:before {
    content: '\e8ba';
}

/* '' */

.uil-clock-eight:before {
    content: '\e8bb';
}

/* '' */

.uil-clock-five:before {
    content: '\e8bc';
}

/* '' */

.uil-clock-nine:before {
    content: '\e8bd';
}

/* '' */

.uil-clock-seven:before {
    content: '\e8be';
}

/* '' */

.uil-clock-ten:before {
    content: '\e8bf';
}

/* '' */

.uil-clock-three:before {
    content: '\e8c0';
}

/* '' */

.uil-clock-two:before {
    content: '\e8c1';
}

/* '' */

.uil-clock:before {
    content: '\e8c2';
}

/* '' */

.uil-closed-captioning:before {
    content: '\e8c3';
}

/* '' */

.uil-cloud-block:before {
    content: '\e8c4';
}

/* '' */

.uil-cloud-bookmark:before {
    content: '\e8c5';
}

/* '' */

.uil-cloud-check:before {
    content: '\e8c6';
}

/* '' */

.uil-cloud-computing:before {
    content: '\e8c7';
}

/* '' */

.uil-cloud-data-connection:before {
    content: '\e8c8';
}

/* '' */

.uil-cloud-database-tree:before {
    content: '\e8c9';
}

/* '' */

.uil-cloud-download:before {
    content: '\e8ca';
}

/* '' */

.uil-cloud-drizzle:before {
    content: '\e8cb';
}

/* '' */

.uil-cloud-exclamation:before {
    content: '\e8cc';
}

/* '' */

.uil-cloud-hail:before {
    content: '\e8cd';
}

/* '' */

.uil-cloud-heart:before {
    content: '\e8ce';
}

/* '' */

.uil-cloud-info:before {
    content: '\e8cf';
}

/* '' */

.uil-cloud-lock:before {
    content: '\e8d0';
}

/* '' */

.uil-cloud-meatball:before {
    content: '\e8d1';
}

/* '' */

.uil-cloud-moon-hail:before {
    content: '\e8d2';
}

/* '' */

.uil-cloud-moon-meatball:before {
    content: '\e8d3';
}

/* '' */

.uil-cloud-moon-rain:before {
    content: '\e8d4';
}

/* '' */

.uil-cloud-moon-showers:before {
    content: '\e8d5';
}

/* '' */

.uil-cloud-moon:before {
    content: '\e8d6';
}

/* '' */

.uil-cloud-question:before {
    content: '\e8d7';
}

/* '' */

.uil-cloud-rain-sun:before {
    content: '\e8d8';
}

/* '' */

.uil-cloud-rain:before {
    content: '\e8d9';
}

/* '' */

.uil-cloud-redo:before {
    content: '\e8da';
}

/* '' */

.uil-cloud-set:before {
    content: '\e8db';
}

/* '' */

.uil-cloud-share:before {
    content: '\e8dc';
}

/* '' */

.uil-cloud-shield:before {
    content: '\e8dd';
}

/* '' */

.uil-cloud-showers-alt:before {
    content: '\e8de';
}

/* '' */

.uil-cloud-showers-heavy:before {
    content: '\e8df';
}

/* '' */

.uil-cloud-showers:before {
    content: '\e8e0';
}

/* '' */

.uil-cloud-slash:before {
    content: '\e8e1';
}

/* '' */

.uil-cloud-sun-hail:before {
    content: '\e8e2';
}

/* '' */

.uil-cloud-sun-meatball:before {
    content: '\e8e3';
}

/* '' */

.uil-cloud-sun-rain-alt:before {
    content: '\e8e4';
}

/* '' */

.uil-cloud-sun-rain:before {
    content: '\e8e5';
}

/* '' */

.uil-cloud-sun-tear:before {
    content: '\e8e6';
}

/* '' */

.uil-cloud-sun:before {
    content: '\e8e7';
}

/* '' */

.uil-cloud-times:before {
    content: '\e8e8';
}

/* '' */

.uil-cloud-unlock:before {
    content: '\e8e9';
}

/* '' */

.uil-cloud-upload:before {
    content: '\e8ea';
}

/* '' */

.uil-cloud-wifi:before {
    content: '\e8eb';
}

/* '' */

.uil-cloud-wind:before {
    content: '\e8ec';
}

/* '' */

.uil-cloud:before {
    content: '\e8ed';
}

/* '' */

.uil-clouds:before {
    content: '\e8ee';
}

/* '' */

.uil-club:before {
    content: '\e8ef';
}

/* '' */

.uil-code:before {
    content: '\e8f0';
}

/* '' */

.uil-coffee:before {
    content: '\e8f1';
}

/* '' */

.uil-cog:before {
    content: '\e8f2';
}

/* '' */

.uil-coins:before {
    content: '\e8f3';
}

/* '' */

.uil-columns:before {
    content: '\e8f4';
}

/* '' */

.uil-comment-alt-block:before {
    content: '\e8f5';
}

/* '' */

.uil-comment-alt-chart-lines:before {
    content: '\e8f6';
}

/* '' */

.uil-comment-alt-check:before {
    content: '\e8f7';
}

/* '' */

.uil-comment-alt-dots:before {
    content: '\e8f8';
}

/* '' */

.uil-comment-alt-download:before {
    content: '\e8f9';
}

/* '' */

.uil-comment-alt-edit:before {
    content: '\e8fa';
}

/* '' */

.uil-comment-alt-exclamation:before {
    content: '\e8fb';
}

/* '' */

.uil-comment-alt-heart:before {
    content: '\e8fc';
}

/* '' */

.uil-comment-alt-image:before {
    content: '\e8fd';
}

/* '' */

.uil-comment-alt-info:before {
    content: '\e8fe';
}

/* '' */

.uil-comment-alt-lines:before {
    content: '\e8ff';
}

/* '' */

.uil-comment-alt-lock:before {
    content: '\e900';
}

/* '' */

.uil-comment-alt-medical:before {
    content: '\e901';
}

/* '' */

.uil-comment-alt-message:before {
    content: '\e902';
}

/* '' */

.uil-comment-alt-notes:before {
    content: '\e903';
}

/* '' */

.uil-comment-alt-plus:before {
    content: '\e904';
}

/* '' */

.uil-comment-alt-question:before {
    content: '\e905';
}

/* '' */

.uil-comment-alt-redo:before {
    content: '\e906';
}

/* '' */

.uil-comment-alt-search:before {
    content: '\e907';
}

/* '' */

.uil-comment-alt-share:before {
    content: '\e908';
}

/* '' */

.uil-comment-alt-shield:before {
    content: '\e909';
}

/* '' */

.uil-comment-alt-upload:before {
    content: '\e90a';
}

/* '' */

.uil-comment-alt-verify:before {
    content: '\e90b';
}

/* '' */

.uil-comment-alt:before {
    content: '\e90c';
}

/* '' */

.uil-comment-block:before {
    content: '\e90d';
}

/* '' */

.uil-comment-chart-line:before {
    content: '\e90e';
}

/* '' */

.uil-comment-check:before {
    content: '\e90f';
}

/* '' */

.uil-comment-dots:before {
    content: '\e910';
}

/* '' */

.uil-comment-download:before {
    content: '\e911';
}

/* '' */

.uil-comment-edit:before {
    content: '\e912';
}

/* '' */

.uil-comment-exclamation:before {
    content: '\e913';
}

/* '' */

.uil-comment-heart:before {
    content: '\e914';
}

/* '' */

.uil-comment-image:before {
    content: '\e915';
}

/* '' */

.uil-comment-info-alt:before {
    content: '\e916';
}

/* '' */

.uil-comment-info:before {
    content: '\e917';
}

/* '' */

.uil-comment-lines:before {
    content: '\e918';
}

/* '' */

.uil-comment-lock:before {
    content: '\e919';
}

/* '' */

.uil-comment-medical:before {
    content: '\e91a';
}

/* '' */

.uil-comment-message:before {
    content: '\e91b';
}

/* '' */

.uil-comment-notes:before {
    content: '\e91c';
}

/* '' */

.uil-comment-plus:before {
    content: '\e91d';
}

/* '' */

.uil-comment-question:before {
    content: '\e91e';
}

/* '' */

.uil-comment-redo:before {
    content: '\e91f';
}

/* '' */

.uil-comment-search:before {
    content: '\e920';
}

/* '' */

.uil-comment-share:before {
    content: '\e921';
}

/* '' */

.uil-comment-shield:before {
    content: '\e922';
}

/* '' */

.uil-comment-slash:before {
    content: '\e923';
}

/* '' */

.uil-comment-upload:before {
    content: '\e924';
}

/* '' */

.uil-comment-verify:before {
    content: '\e925';
}

/* '' */

.uil-comment:before {
    content: '\e926';
}

/* '' */

.uil-comments-alt:before {
    content: '\e927';
}

/* '' */

.uil-comments:before {
    content: '\e928';
}

/* '' */

.uil-commnet-alt-slash:before {
    content: '\e929';
}

/* '' */

.uil-compact-disc:before {
    content: '\e92a';
}

/* '' */

.uil-compass:before {
    content: '\e92b';
}

/* '' */

.uil-compress-alt-left:before {
    content: '\e92c';
}

/* '' */

.uil-compress-alt:before {
    content: '\e92d';
}

/* '' */

.uil-compress-arrows:before {
    content: '\e92e';
}

/* '' */

.uil-compress-lines:before {
    content: '\e92f';
}

/* '' */

.uil-compress-point:before {
    content: '\e930';
}

/* '' */

.uil-compress-v:before {
    content: '\e931';
}

/* '' */

.uil-compress:before {
    content: '\e932';
}

/* '' */

.uil-computer-mouse:before {
    content: '\e933';
}

/* '' */

.uil-confused:before {
    content: '\e934';
}

/* '' */

.uil-constructor:before {
    content: '\e935';
}

/* '' */

.uil-copy-alt:before {
    content: '\e936';
}

/* '' */

.uil-copy-landscape:before {
    content: '\e937';
}

/* '' */

.uil-copy:before {
    content: '\e938';
}

/* '' */

.uil-copyright:before {
    content: '\e939';
}

/* '' */

.uil-corner-down-left:before {
    content: '\e93a';
}

/* '' */

.uil-corner-down-right-alt:before {
    content: '\e93b';
}

/* '' */

.uil-corner-down-right:before {
    content: '\e93c';
}

/* '' */

.uil-corner-left-down:before {
    content: '\e93d';
}

/* '' */

.uil-corner-right-down:before {
    content: '\e93e';
}

/* '' */

.uil-corner-up-left-alt:before {
    content: '\e93f';
}

/* '' */

.uil-corner-up-left:before {
    content: '\e940';
}

/* '' */

.uil-corner-up-right-alt:before {
    content: '\e941';
}

/* '' */

.uil-corner-up-right:before {
    content: '\e942';
}

/* '' */

.uil-creative-commons-pd-alt:before {
    content: '\e943';
}

/* '' */

.uil-creative-commons-pd:before {
    content: '\e944';
}

/* '' */

.uil-crockery:before {
    content: '\e945';
}

/* '' */

.uil-crop-alt-rotate-left:before {
    content: '\e946';
}

/* '' */

.uil-crop-alt-rotate-right:before {
    content: '\e947';
}

/* '' */

.uil-crop-alt:before {
    content: '\e948';
}

/* '' */

.uil-crosshair-alt:before {
    content: '\e949';
}

/* '' */

.uil-crosshair:before {
    content: '\e94a';
}

/* '' */

.uil-crosshairs:before {
    content: '\e94b';
}

/* '' */

.uil-cube:before {
    content: '\e94c';
}

/* '' */

.uil-data-sharing:before {
    content: '\e94d';
}

/* '' */

.uil-database-alt:before {
    content: '\e94e';
}

/* '' */

.uil-database:before {
    content: '\e94f';
}

/* '' */

.uil-desert:before {
    content: '\e950';
}

/* '' */

.uil-desktop-alt-slash:before {
    content: '\e951';
}

/* '' */

.uil-desktop-alt:before {
    content: '\e952';
}

/* '' */

.uil-desktop-cloud-alt:before {
    content: '\e953';
}

/* '' */

.uil-desktop-slash:before {
    content: '\e954';
}

/* '' */

.uil-desktop:before {
    content: '\e955';
}

/* '' */

.uil-dialpad-alt:before {
    content: '\e956';
}

/* '' */

.uil-dialpad:before {
    content: '\e957';
}

/* '' */

.uil-diamond:before {
    content: '\e958';
}

/* '' */

.uil-diary-alt:before {
    content: '\e959';
}

/* '' */

.uil-diary:before {
    content: '\e95a';
}

/* '' */

.uil-dice-five:before {
    content: '\e95b';
}

/* '' */

.uil-dice-four:before {
    content: '\e95c';
}

/* '' */

.uil-dice-one:before {
    content: '\e95d';
}

/* '' */

.uil-dice-six:before {
    content: '\e95e';
}

/* '' */

.uil-dice-three:before {
    content: '\e95f';
}

/* '' */

.uil-dice-two:before {
    content: '\e960';
}

/* '' */

.uil-direction:before {
    content: '\e961';
}

/* '' */

.uil-directions:before {
    content: '\e962';
}

/* '' */

.uil-dizzy-meh:before {
    content: '\e963';
}

/* '' */

.uil-dna:before {
    content: '\e964';
}

/* '' */

.uil-document-layout-center:before {
    content: '\e965';
}

/* '' */

.uil-document-layout-left:before {
    content: '\e966';
}

/* '' */

.uil-document-layout-right:before {
    content: '\e967';
}

/* '' */

.uil-document:before {
    content: '\e968';
}

/* '' */

.uil-dollar-alt:before {
    content: '\e969';
}

/* '' */

.uil-dollar-sign-alt:before {
    content: '\e96a';
}

/* '' */

.uil-dollar-sign:before {
    content: '\e96b';
}

/* '' */

.uil-down-arrow:before {
    content: '\e96c';
}

/* '' */

.uil-download-alt:before {
    content: '\e96d';
}

/* '' */

.uil-dribbble:before {
    content: '\e96e';
}

/* '' */

.uil-drill:before {
    content: '\e96f';
}

/* '' */

.uil-dropbox:before {
    content: '\e970';
}

/* '' */

.uil-dumbbell:before {
    content: '\e971';
}

/* '' */

.uil-edit-alt:before {
    content: '\e972';
}

/* '' */

.uil-edit:before {
    content: '\e973';
}

/* '' */

.uil-ellipsis-h:before {
    content: '\e974';
}

/* '' */

.uil-ellipsis-v:before {
    content: '\e975';
}

/* '' */

.uil-emoji:before {
    content: '\e976';
}

/* '' */

.uil-enter:before {
    content: '\e977';
}

/* '' */

.uil-entry:before {
    content: '\e978';
}

/* '' */

.uil-envelope-add:before {
    content: '\e979';
}

/* '' */

.uil-envelope-alt:before {
    content: '\e97a';
}

/* '' */

.uil-envelope-block:before {
    content: '\e97b';
}

/* '' */

.uil-envelope-bookmark:before {
    content: '\e97c';
}

/* '' */

.uil-envelope-check:before {
    content: '\e97d';
}

/* '' */

.uil-envelope-download-alt:before {
    content: '\e97e';
}

/* '' */

.uil-envelope-download:before {
    content: '\e97f';
}

/* '' */

.uil-envelope-edit:before {
    content: '\e980';
}

/* '' */

.uil-envelope-exclamation:before {
    content: '\e981';
}

/* '' */

.uil-envelope-heart:before {
    content: '\e982';
}

/* '' */

.uil-envelope-info:before {
    content: '\e983';
}

/* '' */

.uil-envelope-lock:before {
    content: '\e984';
}

/* '' */

.uil-envelope-minus:before {
    content: '\e985';
}

/* '' */

.uil-envelope-open:before {
    content: '\e986';
}

/* '' */

.uil-envelope-question:before {
    content: '\e987';
}

/* '' */

.uil-envelope-receive:before {
    content: '\e988';
}

/* '' */

.uil-envelope-redo:before {
    content: '\e989';
}

/* '' */

.uil-envelope-search:before {
    content: '\e98a';
}

/* '' */

.uil-envelope-send:before {
    content: '\e98b';
}

/* '' */

.uil-envelope-share:before {
    content: '\e98c';
}

/* '' */

.uil-envelope-shield:before {
    content: '\e98d';
}

/* '' */

.uil-envelope-star:before {
    content: '\e98e';
}

/* '' */

.uil-envelope-times:before {
    content: '\e98f';
}

/* '' */

.uil-envelope-upload-alt:before {
    content: '\e990';
}

/* '' */

.uil-envelope-upload:before {
    content: '\e991';
}

/* '' */

.uil-envelope:before {
    content: '\e992';
}

/* '' */

.uil-envelopes:before {
    content: '\e993';
}

/* '' */

.uil-equal-circle:before {
    content: '\e994';
}

/* '' */

.uil-euro-circle:before {
    content: '\e995';
}

/* '' */

.uil-euro:before {
    content: '\e996';
}

/* '' */

.uil-exchange-alt:before {
    content: '\e997';
}

/* '' */

.uil-exchange:before {
    content: '\e998';
}

/* '' */

.uil-exclamation-circle:before {
    content: '\e999';
}

/* '' */

.uil-exclamation-octagon:before {
    content: '\e99a';
}

/* '' */

.uil-exclamation-triangle:before {
    content: '\e99b';
}

/* '' */

.uil-exclude:before {
    content: '\e99c';
}

/* '' */

.uil-exit:before {
    content: '\e99d';
}

/* '' */

.uil-expand-alt:before {
    content: '\e99e';
}

/* '' */

.uil-expand-arrows-alt:before {
    content: '\e99f';
}

/* '' */

.uil-expand-arrows:before {
    content: '\e9a0';
}

/* '' */

.uil-expand-from-corner:before {
    content: '\e9a1';
}

/* '' */

.uil-expand-left:before {
    content: '\e9a2';
}

/* '' */

.uil-export:before {
    content: '\e9a3';
}

/* '' */

.uil-exposure-alt:before {
    content: '\e9a4';
}

/* '' */

.uil-exposure-increase:before {
    content: '\e9a5';
}

/* '' */

.uil-external-link-alt:before {
    content: '\e9a6';
}

/* '' */

.uil-eye-slash:before {
    content: '\e9a7';
}

/* '' */

.uil-eye:before {
    content: '\e9a8';
}

/* '' */

.uil-facebook:before {
    content: '\e9a9';
}

/* '' */

.uil-fahrenheit:before {
    content: '\e9aa';
}

/* '' */

.uil-fast-mail-alt:before {
    content: '\e9ab';
}

/* '' */

.uil-fast-mail:before {
    content: '\e9ac';
}

/* '' */

.uil-favorite:before {
    content: '\e9ad';
}

/* '' */

.uil-feedback:before {
    content: '\e9ae';
}

/* '' */

.uil-file-alt:before {
    content: '\e9af';
}

/* '' */

.uil-file-blank:before {
    content: '\e9b0';
}

/* '' */

.uil-file-block-alt:before {
    content: '\e9b1';
}

/* '' */

.uil-file-bookmark-alt:before {
    content: '\e9b2';
}

/* '' */

.uil-file-check-alt:before {
    content: '\e9b3';
}

/* '' */

.uil-file-check:before {
    content: '\e9b4';
}

/* '' */

.uil-file-contract-dollar:before {
    content: '\e9b5';
}

/* '' */

.uil-file-copy-alt:before {
    content: '\e9b6';
}

/* '' */

.uil-file-download-alt:before {
    content: '\e9b7';
}

/* '' */

.uil-file-download:before {
    content: '\e9b8';
}

/* '' */

.uil-file-edit-alt:before {
    content: '\e9b9';
}

/* '' */

.uil-file-exclamation-alt:before {
    content: '\e9ba';
}

/* '' */

.uil-file-exclamation:before {
    content: '\e9bb';
}

/* '' */

.uil-file-heart:before {
    content: '\e9bc';
}

/* '' */

.uil-file-info-alt:before {
    content: '\e9bd';
}

/* '' */

.uil-file-landscape-alt:before {
    content: '\e9be';
}

/* '' */

.uil-file-landscape:before {
    content: '\e9bf';
}

/* '' */

.uil-file-lanscape-slash:before {
    content: '\e9c0';
}

/* '' */

.uil-file-lock-alt:before {
    content: '\e9c1';
}

/* '' */

.uil-file-medical-alt:before {
    content: '\e9c2';
}

/* '' */

.uil-file-medical:before {
    content: '\e9c3';
}

/* '' */

.uil-file-minus-alt:before {
    content: '\e9c4';
}

/* '' */

.uil-file-minus:before {
    content: '\e9c5';
}

/* '' */

.uil-file-network:before {
    content: '\e9c6';
}

/* '' */

.uil-file-plus-alt:before {
    content: '\e9c7';
}

/* '' */

.uil-file-plus:before {
    content: '\e9c8';
}

/* '' */

.uil-file-question-alt:before {
    content: '\e9c9';
}

/* '' */

.uil-file-question:before {
    content: '\e9ca';
}

/* '' */

.uil-file-redo-alt:before {
    content: '\e9cb';
}

/* '' */

.uil-file-search-alt:before {
    content: '\e9cc';
}

/* '' */

.uil-file-share-alt:before {
    content: '\e9cd';
}

/* '' */

.uil-file-shield-alt:before {
    content: '\e9ce';
}

/* '' */

.uil-file-slash:before {
    content: '\e9cf';
}

/* '' */

.uil-file-times-alt:before {
    content: '\e9d0';
}

/* '' */

.uil-file-times:before {
    content: '\e9d1';
}

/* '' */

.uil-file-upload-alt:before {
    content: '\e9d2';
}

/* '' */

.uil-file-upload:before {
    content: '\e9d3';
}

/* '' */

.uil-file:before {
    content: '\e9d4';
}

/* '' */

.uil-files-landscapes-alt:before {
    content: '\e9d5';
}

/* '' */

.uil-files-landscapes:before {
    content: '\e9d6';
}

/* '' */

.uil-film:before {
    content: '\e9d7';
}

/* '' */

.uil-filter-slash:before {
    content: '\e9d8';
}

/* '' */

.uil-filter:before {
    content: '\e9d9';
}

/* '' */

.uil-flask-potion:before {
    content: '\e9da';
}

/* '' */

.uil-flask:before {
    content: '\e9db';
}

/* '' */

.uil-flip-h-alt:before {
    content: '\e9dc';
}

/* '' */

.uil-flip-h:before {
    content: '\e9dd';
}

/* '' */

.uil-flip-v-alt:before {
    content: '\e9de';
}

/* '' */

.uil-flip-v:before {
    content: '\e9df';
}

/* '' */

.uil-flower:before {
    content: '\e9e0';
}

/* '' */

.uil-focus-add:before {
    content: '\e9e1';
}

/* '' */

.uil-focus-target:before {
    content: '\e9e2';
}

/* '' */

.uil-focus:before {
    content: '\e9e3';
}

/* '' */

.uil-folder-check:before {
    content: '\e9e4';
}

/* '' */

.uil-folder-download:before {
    content: '\e9e5';
}

/* '' */

.uil-folder-exclamation:before {
    content: '\e9e6';
}

/* '' */

.uil-folder-heart:before {
    content: '\e9e7';
}

/* '' */

.uil-folder-info:before {
    content: '\e9e8';
}

/* '' */

.uil-folder-lock:before {
    content: '\e9e9';
}

/* '' */

.uil-folder-medical:before {
    content: '\e9ea';
}

/* '' */

.uil-folder-minus:before {
    content: '\e9eb';
}

/* '' */

.uil-folder-network:before {
    content: '\e9ec';
}

/* '' */

.uil-folder-plus:before {
    content: '\e9ed';
}

/* '' */

.uil-folder-question:before {
    content: '\e9ee';
}

/* '' */

.uil-folder-slash:before {
    content: '\e9ef';
}

/* '' */

.uil-folder-times:before {
    content: '\e9f0';
}

/* '' */

.uil-folder-upload:before {
    content: '\e9f1';
}

/* '' */

.uil-folder:before {
    content: '\e9f2';
}

/* '' */

.uil-food:before {
    content: '\e9f3';
}

/* '' */

.uil-football-american:before {
    content: '\e9f4';
}

/* '' */

.uil-football-ball:before {
    content: '\e9f5';
}

/* '' */

.uil-football:before {
    content: '\e9f6';
}

/* '' */

.uil-forecastcloud-moon-tear:before {
    content: '\e9f7';
}

/* '' */

.uil-forwaded-call:before {
    content: '\e9f8';
}

/* '' */

.uil-forward:before {
    content: '\e9f9';
}

/* '' */

.uil-frown:before {
    content: '\e9fa';
}

/* '' */

.uil-game-structure:before {
    content: '\e9fb';
}

/* '' */

.uil-game:before {
    content: '\e9fc';
}

/* '' */

.uil-gift:before {
    content: '\e9fd';
}

/* '' */

.uil-glass-martini-alt-slash:before {
    content: '\e9fe';
}

/* '' */

.uil-glass-martini-alt:before {
    content: '\e9ff';
}

/* '' */

.uil-glass-martini:before {
    content: '\ea00';
}

/* '' */

.uil-glass-tea:before {
    content: '\ea01';
}

/* '' */

.uil-glass:before {
    content: '\ea02';
}

/* '' */

.uil-globe:before {
    content: '\ea03';
}

/* '' */

.uil-gold:before {
    content: '\ea04';
}

/* '' */

.uil-google-drive:before {
    content: '\ea05';
}

/* '' */

.uil-graduation-hat:before {
    content: '\ea06';
}

/* '' */

.uil-graph-bar:before {
    content: '\ea07';
}

/* '' */

.uil-grid:before {
    content: '\ea08';
}

/* '' */

.uil-grids:before {
    content: '\ea09';
}

/* '' */

.uil-grin-tongue-wink-alt:before {
    content: '\ea0a';
}

/* '' */

.uil-grin-tongue-wink:before {
    content: '\ea0b';
}

/* '' */

.uil-grin:before {
    content: '\ea0c';
}

/* '' */

.uil-grip-horizontal-line:before {
    content: '\ea0d';
}

/* '' */

.uil-hdd:before {
    content: '\ea0e';
}

/* '' */

.uil-headphones-alt:before {
    content: '\ea0f';
}

/* '' */

.uil-headphones:before {
    content: '\ea10';
}

/* '' */

.uil-heart-alt:before {
    content: '\ea11';
}

/* '' */

.uil-heart-medical:before {
    content: '\ea12';
}

/* '' */

.uil-heart-rate:before {
    content: '\ea13';
}

/* '' */

.uil-heart-sign:before {
    content: '\ea14';
}

/* '' */

.uil-heart:before {
    content: '\ea15';
}

/* '' */

.uil-heartbeat:before {
    content: '\ea16';
}

/* '' */

.uil-history-alt:before {
    content: '\ea17';
}

/* '' */

.uil-history:before {
    content: '\ea18';
}

/* '' */

.uil-home-alt:before {
    content: '\ea19';
}

/* '' */

.uil-home:before {
    content: '\ea1a';
}

/* '' */

.uil-horizontal-align-center:before {
    content: '\ea1b';
}

/* '' */

.uil-horizontal-align-left:before {
    content: '\ea1c';
}

/* '' */

.uil-horizontal-align-right:before {
    content: '\ea1d';
}

/* '' */

.uil-horizontal-distribution-center:before {
    content: '\ea1e';
}

/* '' */

.uil-horizontal-distribution-left:before {
    content: '\ea1f';
}

/* '' */

.uil-horizontal-distribution-right:before {
    content: '\ea20';
}

/* '' */

.uil-hunting:before {
    content: '\ea21';
}

/* '' */

.uil-image-alt-slash:before {
    content: '\ea22';
}

/* '' */

.uil-image-block:before {
    content: '\ea23';
}

/* '' */

.uil-image-broken:before {
    content: '\ea24';
}

/* '' */

.uil-image-check:before {
    content: '\ea25';
}

/* '' */

.uil-image-download:before {
    content: '\ea26';
}

/* '' */

.uil-image-edit:before {
    content: '\ea27';
}

/* '' */

.uil-image-lock:before {
    content: '\ea28';
}

/* '' */

.uil-image-minus:before {
    content: '\ea29';
}

/* '' */

.uil-image-plus:before {
    content: '\ea2a';
}

/* '' */

.uil-image-question:before {
    content: '\ea2b';
}

/* '' */

.uil-image-redo:before {
    content: '\ea2c';
}

/* '' */

.uil-image-resize-landscape:before {
    content: '\ea2d';
}

/* '' */

.uil-image-resize-square:before {
    content: '\ea2e';
}

/* '' */

.uil-image-search:before {
    content: '\ea2f';
}

/* '' */

.uil-image-share:before {
    content: '\ea30';
}

/* '' */

.uil-image-shield:before {
    content: '\ea31';
}

/* '' */

.uil-image-slash:before {
    content: '\ea32';
}

/* '' */

.uil-image-times:before {
    content: '\ea33';
}

/* '' */

.uil-image-upload:before {
    content: '\ea34';
}

/* '' */

.uil-image-v:before {
    content: '\ea35';
}

/* '' */

.uil-image:before {
    content: '\ea36';
}

/* '' */

.uil-images:before {
    content: '\ea37';
}

/* '' */

.uil-incoming-call:before {
    content: '\ea38';
}

/* '' */

.uil-info-circle:before {
    content: '\ea39';
}

/* '' */

.uil-instagram:before {
    content: '\ea3a';
}

/* '' */

.uil-invoice:before {
    content: '\ea3b';
}

/* '' */

.uil-jackhammer:before {
    content: '\ea3c';
}

/* '' */

.uil-kayak:before {
    content: '\ea3d';
}

/* '' */

.uil-key-skeleton-alt:before {
    content: '\ea3e';
}

/* '' */

.uil-key-skeleton:before {
    content: '\ea3f';
}

/* '' */

.uil-keyboard-alt:before {
    content: '\ea40';
}

/* '' */

.uil-keyboard-hide:before {
    content: '\ea41';
}

/* '' */

.uil-keyboard-show:before {
    content: '\ea42';
}

/* '' */

.uil-keyboard:before {
    content: '\ea43';
}

/* '' */

.uil-keyhole-circle:before {
    content: '\ea44';
}

/* '' */

.uil-keyhole-square-full:before {
    content: '\ea45';
}

/* '' */

.uil-keyhole-square:before {
    content: '\ea46';
}

/* '' */

.uil-kid:before {
    content: '\ea47';
}

/* '' */

.uil-label-alt:before {
    content: '\ea48';
}

/* '' */

.uil-label:before {
    content: '\ea49';
}

/* '' */

.uil-lamp:before {
    content: '\ea4a';
}

/* '' */

.uil-laptop-cloud:before {
    content: '\ea4b';
}

/* '' */

.uil-laptop:before {
    content: '\ea4c';
}

/* '' */

.uil-laughing:before {
    content: '\ea4d';
}

/* '' */

.uil-layer-group-slash:before {
    content: '\ea4e';
}

/* '' */

.uil-layer-group:before {
    content: '\ea4f';
}

/* '' */

.uil-layers-alt:before {
    content: '\ea50';
}

/* '' */

.uil-layers-slash:before {
    content: '\ea51';
}

/* '' */

.uil-layers:before {
    content: '\ea52';
}

/* '' */

.uil-left-arrow-from-left:before {
    content: '\ea53';
}

/* '' */

.uil-left-arrow-to-left:before {
    content: '\ea54';
}

/* '' */

.uil-left-indent-alt:before {
    content: '\ea55';
}

/* '' */

.uil-left-indent:before {
    content: '\ea56';
}

/* '' */

.uil-life-ring:before {
    content: '\ea57';
}

/* '' */

.uil-lightbulb-alt:before {
    content: '\ea58';
}

/* '' */

.uil-lightbulb:before {
    content: '\ea59';
}

/* '' */

.uil-line-alt:before {
    content: '\ea5a';
}

/* '' */

.uil-line-spacing:before {
    content: '\ea5b';
}

/* '' */

.uil-link-alt:before {
    content: '\ea5c';
}

/* '' */

.uil-link-broken:before {
    content: '\ea5d';
}

/* '' */

.uil-link-h:before {
    content: '\ea5e';
}

/* '' */

.uil-link:before {
    content: '\ea5f';
}

/* '' */

.uil-list-ui-alt:before {
    content: '\ea60';
}

/* '' */

.uil-list-ul:before {
    content: '\ea61';
}

/* '' */

.uil-location-arrow-alt:before {
    content: '\ea62';
}

/* '' */

.uil-location-arrow:before {
    content: '\ea63';
}

/* '' */

.uil-location-pin-alt:before {
    content: '\ea64';
}

/* '' */

.uil-location-point:before {
    content: '\ea65';
}

/* '' */

.uil-location:before {
    content: '\ea66';
}

/* '' */

.uil-lock-access:before {
    content: '\ea67';
}

/* '' */

.uil-lock-alt:before {
    content: '\ea68';
}

/* '' */

.uil-lock-open-alt:before {
    content: '\ea69';
}

/* '' */

.uil-lock-slash:before {
    content: '\ea6a';
}

/* '' */

.uil-lock:before {
    content: '\ea6b';
}

/* '' */

.uil-mailbox-alt:before {
    content: '\ea6c';
}

/* '' */

.uil-mailbox:before {
    content: '\ea6d';
}

/* '' */

.uil-map-marker-alt:before {
    content: '\ea6e';
}

/* '' */

.uil-map-marker-edit:before {
    content: '\ea6f';
}

/* '' */

.uil-map-marker-info:before {
    content: '\ea70';
}

/* '' */

.uil-map-marker-minus:before {
    content: '\ea71';
}

/* '' */

.uil-map-marker-plus:before {
    content: '\ea72';
}

/* '' */

.uil-map-marker-question:before {
    content: '\ea73';
}

/* '' */

.uil-map-marker-shield:before {
    content: '\ea74';
}

/* '' */

.uil-map-marker-slash:before {
    content: '\ea75';
}

/* '' */

.uil-map-marker:before {
    content: '\ea76';
}

/* '' */

.uil-map-pin-alt:before {
    content: '\ea77';
}

/* '' */

.uil-map-pin:before {
    content: '\ea78';
}

/* '' */

.uil-map:before {
    content: '\ea79';
}

/* '' */

.uil-mars:before {
    content: '\ea7a';
}

/* '' */

.uil-maximize-left:before {
    content: '\ea7b';
}

/* '' */

.uil-medal:before {
    content: '\ea7c';
}

/* '' */

.uil-medical-drip:before {
    content: '\ea7d';
}

/* '' */

.uil-medical-square-full:before {
    content: '\ea7e';
}

/* '' */

.uil-medical-square:before {
    content: '\ea7f';
}

/* '' */

.uil-medical:before {
    content: '\ea80';
}

/* '' */

.uil-medkit:before {
    content: '\ea81';
}

/* '' */

.uil-meeting-board:before {
    content: '\ea82';
}

/* '' */

.uil-meh-alt:before {
    content: '\ea83';
}

/* '' */

.uil-meh-closed-eye:before {
    content: '\ea84';
}

/* '' */

.uil-meh:before {
    content: '\ea85';
}

/* '' */

.uil-message:before {
    content: '\ea86';
}

/* '' */

.uil-microphone-slash:before {
    content: '\ea87';
}

/* '' */

.uil-microphone:before {
    content: '\ea88';
}

/* '' */

.uil-minus-circle:before {
    content: '\ea89';
}

/* '' */

.uil-minus-path:before {
    content: '\ea8a';
}

/* '' */

.uil-minus-square-full:before {
    content: '\ea8b';
}

/* '' */

.uil-minus-square:before {
    content: '\ea8c';
}

/* '' */

.uil-minus:before {
    content: '\ea8d';
}

/* '' */

.uil-missed-call:before {
    content: '\ea8e';
}

/* '' */

.uil-mobey-bill-slash:before {
    content: '\ea8f';
}

/* '' */

.uil-mobile-android-alt:before {
    content: '\ea90';
}

/* '' */

.uil-mobile-android:before {
    content: '\ea91';
}

/* '' */

.uil-mobile-vibrate:before {
    content: '\ea92';
}

/* '' */

.uil-modem:before {
    content: '\ea93';
}

/* '' */

.uil-money-bill-stack:before {
    content: '\ea94';
}

/* '' */

.uil-money-bill:before {
    content: '\ea95';
}

/* '' */

.uil-money-insert:before {
    content: '\ea96';
}

/* '' */

.uil-money-stack:before {
    content: '\ea97';
}

/* '' */

.uil-money-withdraw:before {
    content: '\ea98';
}

/* '' */

.uil-money-withdrawal:before {
    content: '\ea99';
}

/* '' */

.uil-moneybag-alt:before {
    content: '\ea9a';
}

/* '' */

.uil-moneybag:before {
    content: '\ea9b';
}

/* '' */

.uil-monitor-heart-rate:before {
    content: '\ea9c';
}

/* '' */

.uil-monitor:before {
    content: '\ea9d';
}

/* '' */

.uil-moon-eclipse:before {
    content: '\ea9e';
}

/* '' */

.uil-moon:before {
    content: '\ea9f';
}

/* '' */

.uil-moonset:before {
    content: '\eaa0';
}

/* '' */

.uil-mountains-sun:before {
    content: '\eaa1';
}

/* '' */

.uil-mountains:before {
    content: '\eaa2';
}

/* '' */

.uil-mouse-alt:before {
    content: '\eaa3';
}

/* '' */

.uil-mouse:before {
    content: '\eaa4';
}

/* '' */

.uil-multiply:before {
    content: '\eaa5';
}

/* '' */

.uil-music-note:before {
    content: '\eaa6';
}

/* '' */

.uil-music-tune-slash:before {
    content: '\eaa7';
}

/* '' */

.uil-music:before {
    content: '\eaa8';
}

/* '' */

.uil-n-a:before {
    content: '\eaa9';
}

/* '' */

.uil-navigator:before {
    content: '\eaaa';
}

/* '' */

.uil-nerd:before {
    content: '\eaab';
}

/* '' */

.uil-newspaper:before {
    content: '\eaac';
}

/* '' */

.uil-ninja:before {
    content: '\eaad';
}

/* '' */

.uil-no-entry:before {
    content: '\eaae';
}

/* '' */

.uil-notebooks:before {
    content: '\eaaf';
}

/* '' */

.uil-notes:before {
    content: '\eab0';
}

/* '' */

.uil-object-group:before {
    content: '\eab1';
}

/* '' */

.uil-object-ungroup:before {
    content: '\eab2';
}

/* '' */

.uil-octagon:before {
    content: '\eab3';
}

/* '' */

.uil-outgoing-call:before {
    content: '\eab4';
}

/* '' */

.uil-package:before {
    content: '\eab5';
}

/* '' */

.uil-padlock:before {
    content: '\eab6';
}

/* '' */

.uil-paint-tool:before {
    content: '\eab7';
}

/* '' */

.uil-panorama-h-alt:before {
    content: '\eab8';
}

/* '' */

.uil-panorama-h:before {
    content: '\eab9';
}

/* '' */

.uil-panorama-v:before {
    content: '\eaba';
}

/* '' */

.uil-paperclip:before {
    content: '\eabb';
}

/* '' */

.uil-paragraph:before {
    content: '\eabc';
}

/* '' */

.uil-parcel:before {
    content: '\eabd';
}

/* '' */

.uil-parking-square:before {
    content: '\eabe';
}

/* '' */

.uil-pathfinder-unite:before {
    content: '\eabf';
}

/* '' */

.uil-pathfinder:before {
    content: '\eac0';
}

/* '' */

.uil-pause-circle:before {
    content: '\eac1';
}

/* '' */

.uil-pause:before {
    content: '\eac2';
}

/* '' */

.uil-pen:before {
    content: '\eac3';
}

/* '' */

.uil-pentagon:before {
    content: '\eac4';
}

/* '' */

.uil-phone-alt:before {
    content: '\eac5';
}

/* '' */

.uil-phone-pause:before {
    content: '\eac6';
}

/* '' */

.uil-phone-slash:before {
    content: '\eac7';
}

/* '' */

.uil-phone-times:before {
    content: '\eac8';
}

/* '' */

.uil-phone-volume:before {
    content: '\eac9';
}

/* '' */

.uil-phone:before {
    content: '\eaca';
}

/* '' */

.uil-picture:before {
    content: '\eacb';
}

/* '' */

.uil-plane-arrival:before {
    content: '\eacc';
}

/* '' */

.uil-plane-departure:before {
    content: '\eacd';
}

/* '' */

.uil-plane-fly:before {
    content: '\eace';
}

/* '' */

.uil-plane:before {
    content: '\eacf';
}

/* '' */

.uil-play-circle:before {
    content: '\ead0';
}

/* '' */

.uil-play:before {
    content: '\ead1';
}

/* '' */

.uil-plug:before {
    content: '\ead2';
}

/* '' */

.uil-plus-circle:before {
    content: '\ead3';
}

/* '' */

.uil-plus-square:before {
    content: '\ead4';
}

/* '' */

.uil-plus:before {
    content: '\ead5';
}

/* '' */

.uil-podium:before {
    content: '\ead6';
}

/* '' */

.uil-polygon:before {
    content: '\ead7';
}

/* '' */

.uil-post-stamp:before {
    content: '\ead8';
}

/* '' */

.uil-postcard:before {
    content: '\ead9';
}

/* '' */

.uil-pound-circle:before {
    content: '\eada';
}

/* '' */

.uil-pound:before {
    content: '\eadb';
}

/* '' */

.uil-power:before {
    content: '\eadc';
}

/* '' */

.uil-prescription-bottle:before {
    content: '\eadd';
}

/* '' */

.uil-presentation-check:before {
    content: '\eade';
}

/* '' */

.uil-presentation-edit:before {
    content: '\eadf';
}

/* '' */

.uil-presentation-line:before {
    content: '\eae0';
}

/* '' */

.uil-presentation-lines-alt:before {
    content: '\eae1';
}

/* '' */

.uil-presentation-minus:before {
    content: '\eae2';
}

/* '' */

.uil-presentation-play:before {
    content: '\eae3';
}

/* '' */

.uil-presentation-plus:before {
    content: '\eae4';
}

/* '' */

.uil-presentation-times:before {
    content: '\eae5';
}

/* '' */

.uil-presentation:before {
    content: '\eae6';
}

/* '' */

.uil-previous:before {
    content: '\eae7';
}

/* '' */

.uil-pricetag-alt:before {
    content: '\eae8';
}

/* '' */

.uil-print-slash:before {
    content: '\eae9';
}

/* '' */

.uil-print:before {
    content: '\eaea';
}

/* '' */

.uil-processor:before {
    content: '\eaeb';
}

/* '' */

.uil-pump:before {
    content: '\eaec';
}

/* '' */

.uil-puzzle-piece:before {
    content: '\eaed';
}

/* '' */

.uil-question-circle:before {
    content: '\eaee';
}

/* '' */

.uil-rainbow:before {
    content: '\eaef';
}

/* '' */

.uil-raindrops-alt:before {
    content: '\eaf0';
}

/* '' */

.uil-raindrops:before {
    content: '\eaf1';
}

/* '' */

.uil-receipt-alt:before {
    content: '\eaf2';
}

/* '' */

.uil-receipt:before {
    content: '\eaf3';
}

/* '' */

.uil-record-audio:before {
    content: '\eaf4';
}

/* '' */

.uil-redo:before {
    content: '\eaf5';
}

/* '' */

.uil-refresh:before {
    content: '\eaf6';
}

/* '' */

.uil-registered:before {
    content: '\eaf7';
}

/* '' */

.uil-repeat:before {
    content: '\eaf8';
}

/* '' */

.uil-restaurant:before {
    content: '\eaf9';
}

/* '' */

.uil-right-indent-alt:before {
    content: '\eafa';
}

/* '' */

.uil-rope-way:before {
    content: '\eafb';
}

/* '' */

.uil-rotate-360:before {
    content: '\eafc';
}

/* '' */

.uil-rss-alt:before {
    content: '\eafd';
}

/* '' */

.uil-rss-interface:before {
    content: '\eafe';
}

/* '' */

.uil-rss:before {
    content: '\eaff';
}

/* '' */

.uil-ruler-combined:before {
    content: '\eb00';
}

/* '' */

.uil-ruler:before {
    content: '\eb01';
}

/* '' */

.uil-sad-cry:before {
    content: '\eb02';
}

/* '' */

.uil-sad-crying:before {
    content: '\eb03';
}

/* '' */

.uil-sad-dizzy:before {
    content: '\eb04';
}

/* '' */

.uil-sad-squint:before {
    content: '\eb05';
}

/* '' */

.uil-sad:before {
    content: '\eb06';
}

/* '' */

.uil-scaling-left:before {
    content: '\eb07';
}

/* '' */

.uil-scaling-right:before {
    content: '\eb08';
}

/* '' */

.uil-scenery:before {
    content: '\eb09';
}

/* '' */

.uil-schedule:before {
    content: '\eb0a';
}

/* '' */

.uil-science:before {
    content: '\eb0b';
}

/* '' */

.uil-screw:before {
    content: '\eb0c';
}

/* '' */

.uil-scroll-h:before {
    content: '\eb0d';
}

/* '' */

.uil-scroll:before {
    content: '\eb0e';
}

/* '' */

.uil-search-alt:before {
    content: '\eb0f';
}

/* '' */

.uil-search-minus:before {
    content: '\eb10';
}

/* '' */

.uil-search-plus:before {
    content: '\eb11';
}

/* '' */

.uil-search:before {
    content: '\eb12';
}

/* '' */

.uil-server-alt:before {
    content: '\eb13';
}

/* '' */

.uil-server-connection:before {
    content: '\eb14';
}

/* '' */

.uil-server-network-alt:before {
    content: '\eb15';
}

/* '' */

.uil-server-network:before {
    content: '\eb16';
}

/* '' */

.uil-server:before {
    content: '\eb17';
}

/* '' */

.uil-servers:before {
    content: '\eb18';
}

/* '' */

.uil-servicemark:before {
    content: '\eb19';
}

/* '' */

.uil-share-alt:before {
    content: '\eb1a';
}

/* '' */

.uil-shield-check:before {
    content: '\eb1b';
}

/* '' */

.uil-shield-exclamation:before {
    content: '\eb1c';
}

/* '' */

.uil-shield-question:before {
    content: '\eb1d';
}

/* '' */

.uil-shield-slash:before {
    content: '\eb1e';
}

/* '' */

.uil-shield:before {
    content: '\eb1f';
}

/* '' */

.uil-ship:before {
    content: '\eb20';
}

/* '' */

.uil-shop:before {
    content: '\eb21';
}

/* '' */

.uil-shopping-basket:before {
    content: '\eb22';
}

/* '' */

.uil-shopping-cart-alt:before {
    content: '\eb23';
}

/* '' */

.uil-shopping-trolley:before {
    content: '\eb24';
}

/* '' */

.uil-shovel:before {
    content: '\eb25';
}

/* '' */

.uil-shrink:before {
    content: '\eb26';
}

/* '' */

.uil-shuffle:before {
    content: '\eb27';
}

/* '' */

.uil-shutter-alt:before {
    content: '\eb28';
}

/* '' */

.uil-shutter:before {
    content: '\eb29';
}

/* '' */

.uil-sick:before {
    content: '\eb2a';
}

/* '' */

.uil-sign-alt:before {
    content: '\eb2b';
}

/* '' */

.uil-sign-in-alt:before {
    content: '\eb2c';
}

/* '' */

.uil-sign-left:before {
    content: '\eb2d';
}

/* '' */

.uil-sign-out-alt:before {
    content: '\eb2e';
}

/* '' */

.uil-sign-right:before {
    content: '\eb2f';
}

/* '' */

.uil-signal-alt-3:before {
    content: '\eb30';
}

/* '' */

.uil-signal-alt:before {
    content: '\eb31';
}

/* '' */

.uil-signal:before {
    content: '\eb32';
}

/* '' */

.uil-silence:before {
    content: '\eb33';
}

/* '' */

.uil-silent-squint:before {
    content: '\eb34';
}

/* '' */

.uil-sim-card:before {
    content: '\eb35';
}

/* '' */

.uil-sitemap:before {
    content: '\eb36';
}

/* '' */

.uil-skip-forward-alt:before {
    content: '\eb37';
}

/* '' */

.uil-skip-forward-circle:before {
    content: '\eb38';
}

/* '' */

.uil-skip-forward:before {
    content: '\eb39';
}

/* '' */

.uil-sliders-v-alt:before {
    content: '\eb3a';
}

/* '' */

.uil-sliders-v:before {
    content: '\eb3b';
}

/* '' */

.uil-smile-beam:before {
    content: '\eb3c';
}

/* '' */

.uil-smile-dizzy:before {
    content: '\eb3d';
}

/* '' */

.uil-smile-squint-wink-alt:before {
    content: '\eb3e';
}

/* '' */

.uil-smile-squint-wink:before {
    content: '\eb3f';
}

/* '' */

.uil-smile-wink-alt:before {
    content: '\eb40';
}

/* '' */

.uil-smile-wink:before {
    content: '\eb41';
}

/* '' */

.uil-smile:before {
    content: '\eb42';
}

/* '' */

.uil-snow-flake:before {
    content: '\eb43';
}

/* '' */

.uil-snowflake-alt:before {
    content: '\eb44';
}

/* '' */

.uil-snowflake:before {
    content: '\eb45';
}

/* '' */

.uil-sort-amount-down:before {
    content: '\eb46';
}

/* '' */

.uil-sort-amount-up:before {
    content: '\eb47';
}

/* '' */

.uil-sort:before {
    content: '\eb48';
}

/* '' */

.uil-sorting:before {
    content: '\eb49';
}

/* '' */

.uil-space-key:before {
    content: '\eb4a';
}

/* '' */

.uil-spade:before {
    content: '\eb4b';
}

/* '' */

.uil-sperms:before {
    content: '\eb4c';
}

/* '' */

.uil-spin:before {
    content: '\eb4d';
}

/* '' */

.uil-sport:before {
    content: '\eb4e';
}

/* '' */

.uil-square-full:before {
    content: '\eb4f';
}

/* '' */

.uil-square-shape:before {
    content: '\eb50';
}

/* '' */

.uil-square:before {
    content: '\eb51';
}

/* '' */

.uil-squint:before {
    content: '\eb52';
}

/* '' */

.uil-star-half-alt:before {
    content: '\eb53';
}

/* '' */

.uil-star:before {
    content: '\eb54';
}

/* '' */

.uil-step-backward-alt:before {
    content: '\eb55';
}

/* '' */

.uil-step-backward-circle:before {
    content: '\eb56';
}

/* '' */

.uil-step-backward:before {
    content: '\eb57';
}

/* '' */

.uil-step-forward:before {
    content: '\eb58';
}

/* '' */

.uil-stop-circle:before {
    content: '\eb59';
}

/* '' */

.uil-stopwatch-slash:before {
    content: '\eb5a';
}

/* '' */

.uil-stopwatch:before {
    content: '\eb5b';
}

/* '' */

.uil-store-alt:before {
    content: '\eb5c';
}

/* '' */

.uil-store:before {
    content: '\eb5d';
}

/* '' */

.uil-streering:before {
    content: '\eb5e';
}

/* '' */

.uil-stretcher:before {
    content: '\eb5f';
}

/* '' */

.uil-subject:before {
    content: '\eb60';
}

/* '' */

.uil-subway-alt:before {
    content: '\eb61';
}

/* '' */

.uil-subway:before {
    content: '\eb62';
}

/* '' */

.uil-suitcase-alt:before {
    content: '\eb63';
}

/* '' */

.uil-suitcase:before {
    content: '\eb64';
}

/* '' */

.uil-sun:before {
    content: '\eb65';
}

/* '' */

.uil-sunset:before {
    content: '\eb66';
}

/* '' */

.uil-surprise:before {
    content: '\eb67';
}

/* '' */

.uil-swatchbook:before {
    content: '\eb68';
}

/* '' */

.uil-swimmer:before {
    content: '\eb69';
}

/* '' */

.uil-symbol:before {
    content: '\eb6a';
}

/* '' */

.uil-sync-exclamation:before {
    content: '\eb6b';
}

/* '' */

.uil-sync-slash:before {
    content: '\eb6c';
}

/* '' */

.uil-sync:before {
    content: '\eb6d';
}

/* '' */

.uil-syringe:before {
    content: '\eb6e';
}

/* '' */

.uil-table:before {
    content: '\eb6f';
}

/* '' */

.uil-tablet:before {
    content: '\eb70';
}

/* '' */

.uil-tablets:before {
    content: '\eb71';
}

/* '' */

.uil-tachometer-fast:before {
    content: '\eb72';
}

/* '' */

.uil-tag-alt:before {
    content: '\eb73';
}

/* '' */

.uil-tag:before {
    content: '\eb74';
}

/* '' */

.uil-tape:before {
    content: '\eb75';
}

/* '' */

.uil-taxi:before {
    content: '\eb76';
}

/* '' */

.uil-tear:before {
    content: '\eb77';
}

/* '' */

.uil-technology:before {
    content: '\eb78';
}

/* '' */

.uil-telescope:before {
    content: '\eb79';
}

/* '' */

.uil-temperature-empty:before {
    content: '\eb7a';
}

/* '' */

.uil-temperature-half:before {
    content: '\eb7b';
}

/* '' */

.uil-temperature-minus:before {
    content: '\eb7c';
}

/* '' */

.uil-temperature-plus:before {
    content: '\eb7d';
}

/* '' */

.uil-temperature-quarter:before {
    content: '\eb7e';
}

/* '' */

.uil-temperature-three-quarter:before {
    content: '\eb7f';
}

/* '' */

.uil-temperature:before {
    content: '\eb80';
}

/* '' */

.uil-text:before {
    content: '\eb81';
}

/* '' */

.uil-th-large:before {
    content: '\eb82';
}

/* '' */

.uil-th-slash:before {
    content: '\eb83';
}

/* '' */

.uil-th:before {
    content: '\eb84';
}

/* '' */

.uil-thermometer:before {
    content: '\eb85';
}

/* '' */

.uil-thumbs-down:before {
    content: '\eb86';
}

/* '' */

.uil-thumbs-up:before {
    content: '\eb87';
}

/* '' */

.uil-thunderstorm-moon:before {
    content: '\eb88';
}

/* '' */

.uil-thunderstorm-sun:before {
    content: '\eb89';
}

/* '' */

.uil-thunderstorm:before {
    content: '\eb8a';
}

/* '' */

.uil-ticket:before {
    content: '\eb8b';
}

/* '' */

.uil-times-circle:before {
    content: '\eb8c';
}

/* '' */

.uil-times-square:before {
    content: '\eb8d';
}

/* '' */

.uil-times:before {
    content: '\eb8e';
}

/* '' */

.uil-toggle-off:before {
    content: '\eb8f';
}

/* '' */

.uil-toggle-on:before {
    content: '\eb90';
}

/* '' */

.uil-top-arrow-from-top:before {
    content: '\eb91';
}

/* '' */

.uil-top-arrow-to-top:before {
    content: '\eb92';
}

/* '' */

.uil-tornado:before {
    content: '\eb93';
}

/* '' */

.uil-trademark-circle:before {
    content: '\eb94';
}

/* '' */

.uil-trademark:before {
    content: '\eb95';
}

/* '' */

.uil-traffic-barrier:before {
    content: '\eb96';
}

/* '' */

.uil-trash-alt:before {
    content: '\eb97';
}

/* '' */

.uil-trash:before {
    content: '\eb98';
}

/* '' */

.uil-trees:before {
    content: '\eb99';
}

/* '' */

.uil-triangle:before {
    content: '\eb9a';
}

/* '' */

.uil-trophy:before {
    content: '\eb9b';
}

/* '' */

.uil-trowel:before {
    content: '\eb9c';
}

/* '' */

.uil-truck-case:before {
    content: '\eb9d';
}

/* '' */

.uil-truck-loading:before {
    content: '\eb9e';
}

/* '' */

.uil-truck:before {
    content: '\eb9f';
}

/* '' */

.uil-tv-retro-slash:before {
    content: '\eba0';
}

/* '' */

.uil-tv-retro:before {
    content: '\eba1';
}

/* '' */

.uil-umbrella:before {
    content: '\eba2';
}

/* '' */

.uil-unamused:before {
    content: '\eba3';
}

/* '' */

.uil-unlock-alt:before {
    content: '\eba4';
}

/* '' */

.uil-unlock:before {
    content: '\eba5';
}

/* '' */

.uil-upload-alt:before {
    content: '\eba6';
}

/* '' */

.uil-upload:before {
    content: '\eba7';
}

/* '' */

.uil-usd-circle:before {
    content: '\eba8';
}

/* '' */

.uil-usd-square:before {
    content: '\eba9';
}

/* '' */

.uil-user-check:before {
    content: '\ebaa';
}

/* '' */

.uil-user-circle:before {
    content: '\ebab';
}

/* '' */

.uil-user-exclamation:before {
    content: '\ebac';
}

/* '' */

.uil-user-hard-hat:before {
    content: '\ebad';
}

/* '' */

.uil-user-minus:before {
    content: '\ebae';
}

/* '' */

.uil-user-plus:before {
    content: '\ebaf';
}

/* '' */

.uil-user-square:before {
    content: '\ebb0';
}

/* '' */

.uil-user-times:before {
    content: '\ebb1';
}

/* '' */

.uil-user:before {
    content: '\ebb2';
}

/* '' */

.uil-users-alt:before {
    content: '\ebb3';
}

/* '' */

.uil-utensils-alt:before {
    content: '\ebb4';
}

/* '' */

.uil-utensils:before {
    content: '\ebb5';
}

/* '' */

.uil-vector-square-alt:before {
    content: '\ebb6';
}

/* '' */

.uil-vector-square:before {
    content: '\ebb7';
}

/* '' */

.uil-venus:before {
    content: '\ebb8';
}

/* '' */

.uil-vertical-align-bottom:before {
    content: '\ebb9';
}

/* '' */

.uil-vertical-align-center:before {
    content: '\ebba';
}

/* '' */

.uil-vertical-align-top:before {
    content: '\ebbb';
}

/* '' */

.uil-vertical-distribute-bottom:before {
    content: '\ebbc';
}

/* '' */

.uil-vertical-distribution-center:before {
    content: '\ebbd';
}

/* '' */

.uil-vertical-distribution-top:before {
    content: '\ebbe';
}

/* '' */

.uil-video-slash:before {
    content: '\ebbf';
}

/* '' */

.uil-video:before {
    content: '\ebc0';
}

/* '' */

.uil-voicemail-rectangle:before {
    content: '\ebc1';
}

/* '' */

.uil-voicemail:before {
    content: '\ebc2';
}

/* '' */

.uil-volleyball:before {
    content: '\ebc3';
}

/* '' */

.uil-volume-down:before {
    content: '\ebc4';
}

/* '' */

.uil-volume-mute:before {
    content: '\ebc5';
}

/* '' */

.uil-volume-off:before {
    content: '\ebc6';
}

/* '' */

.uil-volume-up:before {
    content: '\ebc7';
}

/* '' */

.uil-volume:before {
    content: '\ebc8';
}

/* '' */

.uil-wall:before {
    content: '\ebc9';
}

/* '' */

.uil-wallet:before {
    content: '\ebca';
}

/* '' */

.uil-watch-alt:before {
    content: '\ebcb';
}

/* '' */

.uil-watch:before {
    content: '\ebcc';
}

/* '' */

.uil-water-drop-slash:before {
    content: '\ebcd';
}

/* '' */

.uil-water-glass:before {
    content: '\ebce';
}

/* '' */

.uil-water:before {
    content: '\ebcf';
}

/* '' */

.uil-web-grid-alt:before {
    content: '\ebd0';
}

/* '' */

.uil-web-grid:before {
    content: '\ebd1';
}

/* '' */

.uil-web-section-alt:before {
    content: '\ebd2';
}

/* '' */

.uil-web-section:before {
    content: '\ebd3';
}

/* '' */

.uil-webcam:before {
    content: '\ebd4';
}

/* '' */

.uil-weight:before {
    content: '\ebd5';
}

/* '' */

.uil-wheel-barrow:before {
    content: '\ebd6';
}

/* '' */

.uil-wheelchair-alt:before {
    content: '\ebd7';
}

/* '' */

.uil-wheelchair:before {
    content: '\ebd8';
}

/* '' */

.uil-wifi-router:before {
    content: '\ebd9';
}

/* '' */

.uil-wifi-slash:before {
    content: '\ebda';
}

/* '' */

.uil-wifi:before {
    content: '\ebdb';
}

/* '' */

.uil-wind:before {
    content: '\ebdc';
}

/* '' */

.uil-window-grid:before {
    content: '\ebdd';
}

/* '' */

.uil-window-maximize:before {
    content: '\ebde';
}

/* '' */

.uil-window-restore:before {
    content: '\ebdf';
}

/* '' */

.uil-window-section:before {
    content: '\ebe0';
}

/* '' */

.uil-window:before {
    content: '\ebe1';
}

/* '' */

.uil-windsock:before {
    content: '\ebe2';
}

/* '' */

.uil-wrench:before {
    content: '\ebe3';
}

/* '' */

.uil-yellow:before {
    content: '\ebe4';
}

/* '' */

.uil-yen-circle:before {
    content: '\ebe5';
}

/* '' */

.uil-yen:before {
    content: '\ebe6';
}

/* '' */

.uil-youtube-alt:before {
    content: '\ebe7';
}

/* '' */

.uil-youtube:before {
    content: '\ebe8';
}

/* '' */