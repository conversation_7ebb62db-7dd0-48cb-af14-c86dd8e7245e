(function($) {

    "use strict";

    // COLOR MODE
    $('.color-mode').click(function() {
        $('.color-mode-icon').toggleClass('active')
        $('body').toggleClass('dark-mode')
    })

    // HEADER
    $(".navbar").headroom();

    // PROJECT CAROUSEL
    $('.owl-carousel').owlCarousel({
        items: 1,
        loop: true,
        margin: 10,
        nav: true
    });

    // SMOOTHSCROLL
    $(function() {
        $('.nav-link, .custom-btn-link').on('click', function(event) {
            var $anchor = $(this);
            $('html, body').stop().animate({
                scrollTop: $($anchor.attr('href')).offset().top - 49
            }, 1000);
            event.preventDefault();
        });
    });

    // TOOLTIP
    $('.social-links a').tooltip();

    // ========================================
    // MICROINTERACTIONS
    // ========================================

    // Scroll Progress Indicator
    function setupScrollProgress() {
        const progressBar = $('<div class="scroll-progress"></div>');
        $('body').prepend(progressBar);

        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const docHeight = $(document).height() - $(window).height();
            const scrollPercent = (scrollTop / docHeight) * 100;
            $('.scroll-progress').css('width', scrollPercent + '%');
        });
    }

    // Enhanced Button Interactions
    function setupButtonMicrointeractions() {
        // Add bounce effect to buttons
        $('.custom-btn').addClass('bounce-on-hover');

        // Add glow effect to primary buttons
        $('.custom-btn-bg').addClass('glow-on-hover');
    }

    // Tooltip System
    function setupTooltips() {
        // Add tooltips to skill icons
        $('.icon').each(function() {
            const $icon = $(this);
            const skill = $icon.closest('article').find('.skill').text();
            if (skill) {
                $icon.attr('data-tooltip', skill + ' - Hover to see details');
            }
        });

        // Add tooltips to social links
        $('.social-links .uil').each(function() {
            const $link = $(this);
            const href = $link.closest('a').attr('href');
            if (href) {
                if (href.includes('linkedin')) $link.attr('data-tooltip', 'Connect on LinkedIn');
                if (href.includes('github')) $link.attr('data-tooltip', 'View GitHub Profile');
                if (href.includes('twitter')) $link.attr('data-tooltip', 'Follow on Twitter');
                if (href.includes('instagram')) $link.attr('data-tooltip', 'Follow on Instagram');
            }
        });
    }

    // Form Microinteractions
    function setupFormMicrointeractions() {
        $('.form-control').on('focus', function() {
            $(this).closest('.contact-form').addClass('form-focused');
        });

        $('.form-control').on('blur', function() {
            if (!$(this).val()) {
                $(this).closest('.contact-form').removeClass('form-focused');
            }
        });

        // Add shake animation for validation
        $('.submit-btn').on('click', function(e) {
            const form = $(this).closest('form');
            const requiredFields = form.find('[required]');
            let hasErrors = false;

            requiredFields.each(function() {
                if (!$(this).val()) {
                    $(this).addClass('shake');
                    hasErrors = true;
                    setTimeout(() => {
                        $(this).removeClass('shake');
                    }, 500);
                }
            });
        });
    }

    // Navbar Active State
    function setupNavbarMicrointeractions() {
        $(window).on('scroll', function() {
            const scrollPos = $(window).scrollTop() + 100;

            $('.nav-link').each(function() {
                const $link = $(this);
                const href = $link.attr('href');

                if (href && href.startsWith('#')) {
                    const $section = $(href);
                    if ($section.length) {
                        const sectionTop = $section.offset().top;
                        const sectionBottom = sectionTop + $section.outerHeight();

                        if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                            $('.nav-link').removeClass('active');
                            $link.addClass('active');
                        }
                    }
                }
            });
        });
    }

    // Typing Cursor Effect
    function setupTypingCursor() {
        $('.typing-animation').addClass('typing-cursor');

        setTimeout(() => {
            $('.typing-animation').removeClass('typing-cursor');
        }, 3000);
    }

    // ========================================
    // SVG IMAGE ANIMATIONS
    // ========================================

    // Enhanced SVG Animation System
    function setupSVGAnimations() {
        const svgContainer = $('.about-image.svg');

        if (svgContainer.length) {
            // Add particle container
            const particleContainer = $('<div class="svg-particles"></div>');
            svgContainer.append(particleContainer);

            // Create floating particles
            for (let i = 0; i < 3; i++) {
                const particle = $('<div class="svg-particle"></div>');
                particleContainer.append(particle);
            }

            // Add screen glow effect
            const screenGlow = $('<div class="screen-glow"></div>');
            svgContainer.append(screenGlow);

            // Add intersection observer for entrance animation
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        $(entry.target).addClass('svg-animated');

                        // Trigger entrance animation
                        setTimeout(() => {
                            $(entry.target).find('img').css({
                                'animation': 'svg-entrance 1.5s ease-out forwards, float-main 6s ease-in-out infinite 1.5s'
                            });
                        }, 200);
                    }
                });
            }, {
                threshold: 0.3
            });

            observer.observe(svgContainer[0]);
        }
    }

    // Dynamic Code Elements Animation - REMOVED
    function setupDynamicCodeElements() {
        // Code elements removed as requested
        console.log('Dynamic code elements disabled');
    }

    // Mouse Follow Effect for SVG
    function setupSVGMouseFollow() {
        const svgContainer = $('.about-image.svg');

        svgContainer.on('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const rotateX = (y - centerY) / centerY * 5;
            const rotateY = (centerX - x) / centerX * 5;

            $(this).find('img').css({
                transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.02)`
            });
        });

        svgContainer.on('mouseleave', function() {
            $(this).find('img').css({
                transform: 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale(1)'
            });
        });
    }

    // Typing Animation on SVG Screen - REMOVED
    function setupSVGTypingEffect() {
        // Console.log typing effect removed as requested
        console.log('SVG typing effect disabled');
    }

    // Initialize Microinteractions and SVG Animations
    $(document).ready(function() {
        // Microinteractions
        setupScrollProgress();
        setupButtonMicrointeractions();
        setupTooltips();
        setupFormMicrointeractions();
        setupNavbarMicrointeractions();
        setupTypingCursor();

        // SVG Animations
        setupSVGAnimations();
        setupDynamicCodeElements();
        setupSVGMouseFollow();
        setupSVGTypingEffect();

        console.log('Microinteractions and SVG animations initialized!');
    });

})(jQuery);