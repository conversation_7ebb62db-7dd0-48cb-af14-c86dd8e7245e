/**
 * Professional Portfolio JavaScript
 * Enhanced with modern animations and interactions
 */

(function() {
    'use strict';

    // Global variables
    let currentSection = 'home';
    let scrollTimeout;

    // DOM elements
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileNavToggle = document.getElementById('mobile-nav-toggle');
    const navbarNav = document.getElementById('navbar-nav');
    const themeToggle = document.getElementById('theme-toggle');

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
    });

    // Initialize application
    function initializeApp() {
        setupNavigation();
        setupThemeToggle();
        setupScrollAnimations();
        setupTypingAnimation();
        setupCounterAnimations();
        setupFloatingElements();
        setupSmoothScrolling();
        setupParallaxEffects();
    }

    // Navigation functionality
    function setupNavigation() {
        // Mobile navigation toggle
        if (mobileNavToggle && navbarNav) {
            mobileNavToggle.addEventListener('click', function() {
                const isActive = navbarNav.classList.contains('active');
                navbarNav.classList.toggle('active');
                mobileNavToggle.classList.toggle('active');
                mobileNavToggle.setAttribute('aria-expanded', !isActive);
            });
        }

        // Navigation link clicks
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);

                if (targetSection) {
                    // Close mobile menu if open
                    if (navbarNav.classList.contains('active')) {
                        navbarNav.classList.remove('active');
                        mobileNavToggle.classList.remove('active');
                        mobileNavToggle.setAttribute('aria-expanded', 'false');
                    }

                    // Smooth scroll to section
                    targetSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Update active nav link
                    updateActiveNavLink(targetId);
                }
            });
        });

        // Scroll-based navigation updates
        window.addEventListener('scroll', throttle(updateNavigationOnScroll, 100));
    }

    // Update active navigation link
    function updateActiveNavLink(sectionId) {
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${sectionId}`) {
                link.classList.add('active');
            }
        });
        currentSection = sectionId;
    }

    // Update navigation based on scroll position
    function updateNavigationOnScroll() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;

        // Add scrolled class to navbar
        if (navbar) {
            navbar.classList.toggle('scrolled', scrollY > 50);
        }

        // Find current section
        const sections = document.querySelectorAll('section[id]');
        let currentSectionId = 'home';

        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            if (rect.top <= windowHeight * 0.3 && rect.bottom >= windowHeight * 0.3) {
                currentSectionId = section.id;
            }
        });

        if (currentSectionId !== currentSection) {
            updateActiveNavLink(currentSectionId);
        }
    }

    // Theme toggle functionality
    function setupThemeToggle() {
        if (!themeToggle) return;

        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);

        themeToggle.addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            // Add animation class
            document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        });
    }

    // Scroll animations
    function setupScrollAnimations() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');

        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');

                    // Add staggered animation for child elements
                    const children = entry.target.querySelectorAll('.stagger-item');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animated');
                        }, index * 100);
                    });
                }
            });
        }, observerOptions);

        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }

    // Typing animation
    function setupTypingAnimation() {
        const typingElement = document.querySelector('.typing-animation');
        if (!typingElement) return;

        const text = typingElement.textContent;
        typingElement.textContent = '';
        typingElement.style.borderRight = '2px solid var(--primary-color)';

        let index = 0;
        const typeSpeed = 100;

        function typeCharacter() {
            if (index < text.length) {
                typingElement.textContent += text.charAt(index);
                index++;
                setTimeout(typeCharacter, typeSpeed);
            } else {
                // Remove cursor after typing is complete
                setTimeout(() => {
                    typingElement.style.borderRight = 'none';
                }, 1000);
            }
        }

        // Start typing animation when element is visible
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(typeCharacter, 1000);
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(typingElement);
    }

    // Counter animations
    function setupCounterAnimations() {
        const counters = document.querySelectorAll('[data-count]');

        const animateCounter = (counter) => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;

            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            updateCounter();
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });

        counters.forEach(counter => {
            observer.observe(counter);
        });
    }

    // Floating elements animation
    function setupFloatingElements() {
        const floatingElements = document.querySelectorAll('.floating-element');

        floatingElements.forEach((element, index) => {
            const speed = parseFloat(element.getAttribute('data-speed')) || 1;
            const delay = index * 0.5;

            element.style.animationDelay = `${delay}s`;
            element.style.animationDuration = `${3 / speed}s`;
            element.classList.add('floating');
        });
    }

    // Smooth scrolling for anchor links
    function setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Parallax effects
    function setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.parallax');

        window.addEventListener('scroll', throttle(() => {
            const scrolled = window.pageYOffset;

            parallaxElements.forEach(element => {
                const speed = element.getAttribute('data-speed') || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        }, 16));
    }

    // Utility function: Throttle
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Utility function: Debounce
    function debounce(func, wait, immediate) {
        let timeout;
        return function() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Enhanced carousel functionality
    function setupEnhancedCarousel() {
        const carousel = document.querySelector('.owl-carousel');
        if (carousel && typeof $ !== 'undefined' && $.fn.owlCarousel) {
            $(carousel).owlCarousel({
                items: 1,
                loop: true,
                margin: 30,
                nav: true,
                dots: true,
                autoplay: true,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                animateOut: 'fadeOut',
                animateIn: 'fadeIn',
                navText: [
                    '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>',
                    '<svg fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>'
                ],
                responsive: {
                    0: { items: 1 },
                    768: { items: 1 },
                    1024: { items: 1 }
                }
            });
        }
    }

    // Initialize carousel after other components
    setTimeout(setupEnhancedCarousel, 100);

    // Performance optimization: Reduce animations on low-end devices
    function optimizeForPerformance() {
        const isLowEndDevice = navigator.hardwareConcurrency < 4 ||
                              navigator.deviceMemory < 4 ||
                              /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        if (isLowEndDevice) {
            document.documentElement.classList.add('reduced-motion');
        }
    }

    optimizeForPerformance();

    // Expose some functions globally for debugging
    window.portfolioApp = {
        updateActiveNavLink,
        currentSection: () => currentSection
    };

})();